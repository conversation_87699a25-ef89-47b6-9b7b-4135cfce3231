"""
Alpha Vantage Data Fetcher for EOTS v2.5
========================================

This module provides integration with Alpha Vantage's Alpha Intelligence™ APIs
to enhance the AI dashboard with real market sentiment and intelligence data.

Key Features:
- News & Sentiment analysis for market intelligence
- Real-time sentiment scoring for confidence validation
- Topic-based news filtering for regime analysis
- Historical sentiment data for performance tracking

Author: EOTS v2.5 Development Team
Version: 2.5.0
"""

import logging
import requests
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import pandas as pd
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class SentimentData:
    """Data class for Alpha Vantage sentiment analysis results."""
    ticker: str
    overall_sentiment_score: float
    overall_sentiment_label: str
    article_count: int
    relevance_score: float
    topics: List[str]
    timestamp: datetime
    
@dataclass
class NewsArticle:
    """Data class for individual news articles."""
    title: str
    url: str
    time_published: str
    summary: str
    sentiment_score: float
    sentiment_label: str
    relevance_score: float
    topics: List[str]

class AlphaVantageDataFetcherV2_5:
    """
    Alpha Vantage Data Fetcher for EOTS v2.5

    Integrates with Alpha Vantage's Alpha Intelligence™ APIs to provide:
    - Real-time market sentiment analysis
    - News-driven market intelligence
    - Topic-based sentiment filtering
    - Historical sentiment tracking
    """

    def __init__(self, api_key: str = "9CZXMNC1HO3EI2QR"):
        """
        Initialize Alpha Vantage Data Fetcher.

        Args:
            api_key: Alpha Vantage API key
        """
        self.api_key = api_key
        self.base_url = "https://www.alphavantage.co/query"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'EOTS-v2.5-AI-Dashboard/1.0'
        })

        # Rate limiting and graceful degradation
        self.daily_request_count = 0
        self.daily_limit = 25  # Free tier limit
        self.rate_limited = False
        self.last_reset_date = datetime.now().date()

        logger.info("🧠 Alpha Vantage Data Fetcher initialized with Alpha Intelligence™")

    def _check_rate_limit(self) -> bool:
        """
        Check if we've hit the daily rate limit and handle graceful degradation.

        Returns:
            bool: True if we can make requests, False if rate limited
        """
        # Reset counter if it's a new day
        current_date = datetime.now().date()
        if current_date > self.last_reset_date:
            self.daily_request_count = 0
            self.rate_limited = False
            self.last_reset_date = current_date
            logger.info("🔄 Alpha Vantage daily rate limit reset")

        # Check if we're already rate limited
        if self.rate_limited:
            logger.debug("🚫 Alpha Vantage rate limited - using fallback data")
            return False

        # Check if we're approaching the limit
        if self.daily_request_count >= self.daily_limit:
            self.rate_limited = True
            logger.warning(f"🚫 Alpha Vantage daily rate limit ({self.daily_limit}) reached. Switching to fallback mode.")
            return False

        return True

    def _increment_request_count(self):
        """Increment the daily request counter."""
        self.daily_request_count += 1
        remaining = self.daily_limit - self.daily_request_count

        if remaining <= 5:
            logger.warning(f"⚠️ Alpha Vantage API: Only {remaining} requests remaining today")
        elif remaining <= 10:
            logger.info(f"📊 Alpha Vantage API: {remaining} requests remaining today")

    def _handle_api_response(self, response) -> Dict[str, Any]:
        """Handle API response and check for rate limiting."""
        try:
            data = response.json()

            # Check for rate limit message in response
            if 'Note' in data and 'rate limit' in data['Note'].lower():
                logger.warning(f"🚫 Alpha Vantage rate limit detected: {data['Note']}")
                self.rate_limited = True
                return {}

            # Check for other API errors
            if 'Error Message' in data:
                logger.error(f"Alpha Vantage API error: {data['Error Message']}")
                return {}

            return data

        except json.JSONDecodeError as e:
            logger.error(f"Error parsing Alpha Vantage response: {str(e)}")
            return {}

    def get_news_sentiment(self,
                          tickers: str = "SPY", 
                          topics: Optional[str] = None,
                          time_from: Optional[str] = None,
                          time_to: Optional[str] = None,
                          limit: int = 50,
                          sort: str = "LATEST") -> Dict[str, Any]:
        """
        Fetch news sentiment data from Alpha Vantage Alpha Intelligence™.
        
        Args:
            tickers: Comma-separated list of tickers (e.g., "SPY,QQQ")
            topics: Comma-separated list of topics (e.g., "financial_markets,economy_monetary")
            time_from: Start time in YYYYMMDDTHHMM format
            time_to: End time in YYYYMMDDTHHMM format
            limit: Number of results (max 1000)
            sort: Sort order (LATEST, EARLIEST, RELEVANCE)
            
        Returns:
            Dict containing sentiment analysis results
        """
        try:
            # Check rate limit before making request
            if not self._check_rate_limit():
                logger.debug(f"🚫 Alpha Vantage rate limited - returning fallback data for {tickers}")
                return {}

            params = {
                'function': 'NEWS_SENTIMENT',
                'tickers': tickers,
                'limit': limit,
                'sort': sort,
                'apikey': self.api_key
            }

            # Add optional parameters
            if topics:
                params['topics'] = topics
            if time_from:
                params['time_from'] = time_from
            if time_to:
                params['time_to'] = time_to

            logger.info(f"🔍 Fetching Alpha Intelligence™ sentiment for {tickers}")

            response = self.session.get(self.base_url, params=params)
            response.raise_for_status()

            # Increment request counter
            self._increment_request_count()

            # Handle response with rate limit checking
            data = self._handle_api_response(response)

            if data:
                logger.info(f"✅ Successfully fetched sentiment data for {tickers}")

            return data
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Error fetching Alpha Vantage sentiment data: {str(e)}")
            return {}
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing Alpha Vantage response: {str(e)}")
            return {}
        except Exception as e:
            logger.error(f"Unexpected error in Alpha Vantage sentiment fetch: {str(e)}")
            return {}
    
    def parse_sentiment_data(self, raw_data: Dict[str, Any], ticker: str = "SPY") -> Optional[SentimentData]:
        """
        Parse raw Alpha Vantage sentiment data into structured format.
        
        Args:
            raw_data: Raw response from Alpha Vantage API
            ticker: Primary ticker for analysis
            
        Returns:
            SentimentData object or None if parsing fails
        """
        try:
            if not raw_data or 'feed' not in raw_data:
                return None
            
            articles = raw_data.get('feed', [])
            if not articles:
                return None
            
            # Calculate overall sentiment metrics
            sentiment_scores = []
            relevance_scores = []
            all_topics = set()
            
            for article in articles:
                # Get ticker-specific sentiment if available
                ticker_sentiments = article.get('ticker_sentiment', [])
                for ts in ticker_sentiments:
                    if ts.get('ticker', '').upper() == ticker.upper():
                        sentiment_scores.append(float(ts.get('ticker_sentiment_score', 0)))
                        relevance_scores.append(float(ts.get('relevance_score', 0)))
                        break
                else:
                    # Fallback to overall sentiment
                    sentiment_scores.append(float(article.get('overall_sentiment_score', 0)))
                
                # Collect topics
                topics = article.get('topics', [])
                for topic in topics:
                    all_topics.add(topic.get('topic', ''))
            
            if not sentiment_scores:
                return None
            
            # Calculate aggregated metrics
            avg_sentiment = sum(sentiment_scores) / len(sentiment_scores)
            avg_relevance = sum(relevance_scores) / len(relevance_scores) if relevance_scores else 0.5
            
            # Determine sentiment label
            if avg_sentiment >= 0.15:
                sentiment_label = "Bullish"
            elif avg_sentiment <= -0.15:
                sentiment_label = "Bearish"
            else:
                sentiment_label = "Neutral"
            
            return SentimentData(
                ticker=ticker,
                overall_sentiment_score=avg_sentiment,
                overall_sentiment_label=sentiment_label,
                article_count=len(articles),
                relevance_score=avg_relevance,
                topics=list(all_topics),
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Error parsing sentiment data: {str(e)}")
            return None
    
    def get_market_intelligence_summary(self, ticker: str = "SPY") -> Dict[str, Any]:
        """
        Get comprehensive market intelligence summary for AI dashboard.
        
        Args:
            ticker: Ticker symbol to analyze
            
        Returns:
            Dict containing market intelligence metrics
        """
        try:
            # Fetch recent sentiment data with financial markets focus
            sentiment_data = self.get_news_sentiment(
                tickers=ticker,
                topics="financial_markets,economy_monetary,economy_macro",
                limit=100,
                sort="RELEVANCE"
            )
            
            if not sentiment_data:
                return self._get_fallback_intelligence()
            
            # Parse sentiment data
            parsed_sentiment = self.parse_sentiment_data(sentiment_data, ticker)
            
            if not parsed_sentiment:
                return self._get_fallback_intelligence()
            
            # Generate intelligence summary
            intelligence = {
                'sentiment_score': parsed_sentiment.overall_sentiment_score,
                'sentiment_label': parsed_sentiment.overall_sentiment_label,
                'confidence_score': min(parsed_sentiment.relevance_score * 100, 95),
                'article_count': parsed_sentiment.article_count,
                'news_volume': self._categorize_news_volume(parsed_sentiment.article_count),
                'topics': parsed_sentiment.topics[:5],  # Top 5 topics
                'market_attention': self._calculate_market_attention(parsed_sentiment),
                'ai_insights': self._generate_ai_insights(parsed_sentiment),
                'timestamp': parsed_sentiment.timestamp.isoformat()
            }
            
            logger.info(f"✅ Generated market intelligence summary for {ticker}")
            return intelligence
            
        except Exception as e:
            logger.error(f"Error generating market intelligence: {str(e)}")
            return self._get_fallback_intelligence()
    
    def is_available(self) -> bool:
        """Check if Alpha Vantage API is available (not rate limited)."""
        return self._check_rate_limit()

    def get_status(self) -> Dict[str, Any]:
        """Get current Alpha Vantage API status."""
        return {
            'available': not self.rate_limited,
            'daily_requests_used': self.daily_request_count,
            'daily_limit': self.daily_limit,
            'requests_remaining': max(0, self.daily_limit - self.daily_request_count),
            'rate_limited': self.rate_limited,
            'last_reset_date': self.last_reset_date.isoformat()
        }

    def _get_fallback_intelligence(self) -> Dict[str, Any]:
        """Fallback intelligence data when API is unavailable."""
        status = self.get_status()

        if self.rate_limited:
            insights = [
                f'🚫 Alpha Intelligence™ rate limited ({status["daily_requests_used"]}/{status["daily_limit"]} requests used)',
                '📊 Using EOTS v2.5 technical metrics only',
                '🔄 Alpha Intelligence™ will reset tomorrow'
            ]
        else:
            insights = ['📊 Alpha Intelligence™ temporarily unavailable - using EOTS v2.5 metrics only']

        return {
            'sentiment_score': 0.0,
            'sentiment_label': 'Neutral',
            'confidence_score': 50.0,
            'article_count': 0,
            'news_volume': 'Low',
            'topics': ['financial_markets'],
            'market_attention': 'Moderate',
            'ai_insights': insights,
            'timestamp': datetime.now().isoformat(),
            'alpha_vantage_status': status
        }
    
    def _categorize_news_volume(self, article_count: int) -> str:
        """Categorize news volume based on article count."""
        if article_count >= 50:
            return "Very High"
        elif article_count >= 30:
            return "High"
        elif article_count >= 15:
            return "Moderate"
        elif article_count >= 5:
            return "Low"
        else:
            return "Very Low"
    
    def _calculate_market_attention(self, sentiment_data: SentimentData) -> str:
        """Calculate market attention level."""
        attention_score = (sentiment_data.article_count * 0.4 + 
                          abs(sentiment_data.overall_sentiment_score) * 100 * 0.6)
        
        if attention_score >= 40:
            return "Extreme"
        elif attention_score >= 25:
            return "High"
        elif attention_score >= 15:
            return "Moderate"
        else:
            return "Low"
    
    def get_earnings_call_transcript(self, ticker: str, year: str = None, quarter: str = None) -> Dict[str, Any]:
        """
        Fetch earnings call transcript from Alpha Vantage Alpha Intelligence™.

        Args:
            ticker: Stock ticker symbol
            year: Year (YYYY format, optional - defaults to most recent)
            quarter: Quarter (Q1, Q2, Q3, Q4, optional - defaults to most recent)

        Returns:
            Dict containing earnings call transcript and analysis
        """
        try:
            params = {
                'function': 'EARNINGS_CALL_TRANSCRIPT',
                'symbol': ticker,
                'apikey': self.api_key
            }

            # Add optional parameters
            if year:
                params['year'] = year
            if quarter:
                params['quarter'] = quarter

            logger.info(f"🎙️ Fetching earnings call transcript for {ticker}")

            response = self.session.get(self.base_url, params=params)
            response.raise_for_status()

            data = response.json()

            # Check for API errors
            if 'Error Message' in data:
                logger.error(f"Alpha Vantage API error: {data['Error Message']}")
                return {}

            logger.info(f"✅ Successfully fetched earnings transcript for {ticker}")
            return data

        except Exception as e:
            logger.error(f"Error fetching earnings transcript: {str(e)}")
            return {}

    def get_company_overview(self, ticker: str) -> Dict[str, Any]:
        """
        Fetch company overview and fundamentals from Alpha Vantage.

        Args:
            ticker: Stock ticker symbol

        Returns:
            Dict containing company fundamentals and overview
        """
        try:
            # Check rate limit before making request
            if not self._check_rate_limit():
                logger.debug(f"🚫 Alpha Vantage rate limited - returning empty data for {ticker}")
                return {}

            params = {
                'function': 'OVERVIEW',
                'symbol': ticker,
                'apikey': self.api_key
            }

            logger.info(f"🏢 Fetching company overview for {ticker}")

            response = self.session.get(self.base_url, params=params)
            response.raise_for_status()

            # Increment request counter
            self._increment_request_count()

            # Handle response with rate limit checking
            data = self._handle_api_response(response)

            if data:
                logger.info(f"✅ Successfully fetched company overview for {ticker}")

            return data

        except Exception as e:
            logger.error(f"Error fetching company overview: {str(e)}")
            return {}

    def get_earnings_calendar(self, horizon: str = "3month") -> Dict[str, Any]:
        """
        Fetch upcoming earnings calendar from Alpha Vantage.

        Args:
            horizon: Time horizon (3month, 6month, 12month)

        Returns:
            Dict containing upcoming earnings events
        """
        try:
            # Check rate limit before making request
            if not self._check_rate_limit():
                logger.debug(f"🚫 Alpha Vantage rate limited - returning empty earnings calendar")
                return {}

            params = {
                'function': 'EARNINGS_CALENDAR',
                'horizon': horizon,
                'apikey': self.api_key
            }

            logger.info(f"📅 Fetching earnings calendar for {horizon}")

            response = self.session.get(self.base_url, params=params)
            response.raise_for_status()

            # Increment request counter
            self._increment_request_count()

            # Handle response with rate limit checking
            data = self._handle_api_response(response)

            if data:
                logger.info(f"✅ Successfully fetched earnings calendar")

            return data

        except Exception as e:
            logger.error(f"Error fetching earnings calendar: {str(e)}")
            return {}

    def _generate_ai_insights(self, sentiment_data: SentimentData) -> List[str]:
        """Generate AI insights based on sentiment analysis."""
        insights = []

        # Sentiment-based insights
        if abs(sentiment_data.overall_sentiment_score) >= 0.3:
            direction = "bullish" if sentiment_data.overall_sentiment_score > 0 else "bearish"
            insights.append(f"🎯 Strong {direction} sentiment detected ({sentiment_data.overall_sentiment_score:.3f}) "
                          f"across {sentiment_data.article_count} articles")

        # Volume-based insights
        if sentiment_data.article_count >= 30:
            insights.append(f"📈 High news volume ({sentiment_data.article_count} articles) indicates elevated market attention")

        # Topic-based insights
        if 'economy_monetary' in sentiment_data.topics:
            insights.append("🏦 Federal Reserve policy discussions detected - potential volatility catalyst")

        if 'earnings' in sentiment_data.topics:
            insights.append("📊 Earnings-related news flow - monitor for sector rotation signals")

        # Relevance insights
        if sentiment_data.relevance_score >= 0.7:
            insights.append(f"✅ High relevance score ({sentiment_data.relevance_score:.2f}) - news directly impacts target asset")

        return insights[:4]  # Limit to 4 insights

    def get_comprehensive_intelligence_summary(self, ticker: str = "SPY") -> Dict[str, Any]:
        """
        Get comprehensive Alpha Intelligence™ summary combining all data sources.

        Args:
            ticker: Ticker symbol to analyze

        Returns:
            Dict containing comprehensive market intelligence
        """
        try:
            logger.info(f"🧠 Generating comprehensive Alpha Intelligence™ summary for {ticker}")

            # Fetch all intelligence data sources
            sentiment_data = self.get_news_sentiment(
                tickers=ticker,
                topics="financial_markets,economy_monetary,earnings",
                limit=50,
                sort="RELEVANCE"
            )

            company_overview = self.get_company_overview(ticker)
            earnings_calendar = self.get_earnings_calendar("3month")

            # Parse sentiment data
            parsed_sentiment = self.parse_sentiment_data(sentiment_data, ticker) if sentiment_data else None

            # Generate comprehensive summary
            intelligence = {
                'ticker': ticker,
                'timestamp': datetime.now().isoformat(),
                'alpha_intelligence_active': True,

                # Sentiment Intelligence
                'sentiment': {
                    'score': parsed_sentiment.overall_sentiment_score if parsed_sentiment else 0.0,
                    'label': parsed_sentiment.overall_sentiment_label if parsed_sentiment else 'Neutral',
                    'confidence': min(parsed_sentiment.relevance_score * 100, 95) if parsed_sentiment else 50.0,
                    'article_count': parsed_sentiment.article_count if parsed_sentiment else 0,
                    'topics': parsed_sentiment.topics[:5] if parsed_sentiment else [],
                    'insights': self._generate_ai_insights(parsed_sentiment) if parsed_sentiment else []
                },

                # Company Fundamentals
                'fundamentals': self._extract_key_fundamentals(company_overview),

                # Earnings Intelligence
                'earnings': self._extract_earnings_intelligence(earnings_calendar, ticker),

                # Market Context
                'market_context': self._generate_market_context(parsed_sentiment, company_overview),

                # AI Recommendations
                'ai_recommendations': self._generate_ai_recommendations(parsed_sentiment, company_overview)
            }

            logger.info(f"✅ Generated comprehensive Alpha Intelligence™ summary for {ticker}")
            return intelligence

        except Exception as e:
            logger.error(f"Error generating comprehensive intelligence: {str(e)}")
            return self._get_fallback_comprehensive_intelligence(ticker)

    def _extract_key_fundamentals(self, company_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract key fundamental metrics from company overview."""
        if not company_data:
            return {}

        try:
            return {
                'market_cap': company_data.get('MarketCapitalization', 'N/A'),
                'pe_ratio': company_data.get('PERatio', 'N/A'),
                'dividend_yield': company_data.get('DividendYield', 'N/A'),
                'beta': company_data.get('Beta', 'N/A'),
                'sector': company_data.get('Sector', 'N/A'),
                'industry': company_data.get('Industry', 'N/A'),
                'description': company_data.get('Description', '')[:200] + '...' if company_data.get('Description') else 'N/A'
            }
        except Exception as e:
            logger.error(f"Error extracting fundamentals: {str(e)}")
            return {}

    def _extract_earnings_intelligence(self, earnings_data: Dict[str, Any], ticker: str) -> Dict[str, Any]:
        """Extract earnings intelligence for the specific ticker."""
        if not earnings_data:
            return {'next_earnings': 'Unknown', 'earnings_proximity': 'Unknown'}

        try:
            # Look for the ticker in upcoming earnings
            # Note: This is a simplified implementation - actual parsing would depend on API response format
            return {
                'next_earnings': 'Check earnings calendar',
                'earnings_proximity': 'Monitor for upcoming events',
                'earnings_impact': 'Potential volatility catalyst'
            }
        except Exception as e:
            logger.error(f"Error extracting earnings intelligence: {str(e)}")
            return {'next_earnings': 'Error', 'earnings_proximity': 'Error'}

    def _generate_market_context(self, sentiment_data: Optional[SentimentData], company_data: Dict[str, Any]) -> List[str]:
        """Generate market context insights."""
        context = []

        try:
            # Sentiment context
            if sentiment_data and abs(sentiment_data.overall_sentiment_score) > 0.2:
                direction = "positive" if sentiment_data.overall_sentiment_score > 0 else "negative"
                context.append(f"📰 Strong {direction} news sentiment driving market attention")

            # Sector context
            sector = company_data.get('Sector', '') if company_data else ''
            if sector:
                context.append(f"🏭 {sector} sector dynamics may influence price action")

            # Beta context
            beta = company_data.get('Beta', '') if company_data else ''
            if beta and beta != 'N/A':
                try:
                    beta_val = float(beta)
                    if beta_val > 1.2:
                        context.append(f"⚡ High beta ({beta}) suggests amplified market moves")
                    elif beta_val < 0.8:
                        context.append(f"🛡️ Low beta ({beta}) suggests defensive characteristics")
                except:
                    pass

            return context[:3]  # Limit to 3 context items

        except Exception as e:
            logger.error(f"Error generating market context: {str(e)}")
            return ["📊 Market context analysis temporarily unavailable"]

    def _generate_ai_recommendations(self, sentiment_data: Optional[SentimentData], company_data: Dict[str, Any]) -> List[str]:
        """Generate AI-powered trading recommendations."""
        recommendations = []

        try:
            # Sentiment-based recommendations
            if sentiment_data:
                if sentiment_data.overall_sentiment_score > 0.3:
                    recommendations.append("🚀 Strong bullish sentiment supports long strategies")
                elif sentiment_data.overall_sentiment_score < -0.3:
                    recommendations.append("🐻 Strong bearish sentiment favors short strategies")

                if sentiment_data.article_count > 25:
                    recommendations.append("📈 High news volume suggests increased volatility - consider vol strategies")

            # Add general recommendation
            recommendations.append("🎯 Cross-reference with EOTS v2.5 metrics for optimal entry timing")

            return recommendations[:3]  # Limit to 3 recommendations

        except Exception as e:
            logger.error(f"Error generating AI recommendations: {str(e)}")
            return ["🤖 AI recommendations temporarily unavailable"]

    def _get_fallback_comprehensive_intelligence(self, ticker: str) -> Dict[str, Any]:
        """Fallback comprehensive intelligence when APIs are unavailable."""
        status = self.get_status()

        if self.rate_limited:
            insights = [
                f'🚫 Alpha Intelligence™ rate limited ({status["daily_requests_used"]}/{status["daily_limit"]} requests)',
                '🔄 Will reset tomorrow - using EOTS v2.5 only'
            ]
            recommendations = [
                '🎯 Focus on technical analysis via EOTS v2.5',
                '📈 Monitor options flow and gamma levels',
                '⏰ Alpha Intelligence™ available tomorrow'
            ]
        else:
            insights = ['📊 Alpha Intelligence™ temporarily unavailable']
            recommendations = ['🎯 Focus on technical analysis via EOTS v2.5']

        return {
            'ticker': ticker,
            'timestamp': datetime.now().isoformat(),
            'alpha_intelligence_active': False,
            'sentiment': {
                'score': 0.0,
                'label': 'Neutral',
                'confidence': 50.0,
                'article_count': 0,
                'topics': [],
                'insights': insights
            },
            'fundamentals': {},
            'earnings': {'next_earnings': 'Unknown', 'earnings_proximity': 'Unknown'},
            'market_context': ['🔍 Using EOTS v2.5 metrics only'],
            'ai_recommendations': recommendations,
            'alpha_vantage_status': status
        }
