# core_analytics_engine/pydantic_ai_atif_engine_v2_5.py
# EOTS v2.5 - PYDANTIC AI ENHANCED ATIF ENGINE

import logging
import asyncio
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timed<PERSON>ta
from dataclasses import dataclass
import json

from pydantic_ai import Agent
from pydantic_ai.models.openai import OpenAIModel
from pydantic import BaseModel, Field

from data_models.eots_schemas_v2_5 import (
    ProcessedDataBundleV2_5, ATIFStrategyDirectivePayloadV2_5,
    ProcessedUnderlyingAggregatesV2_5, ATIFSituationalAssessmentProfileV2_5
)
from utils.config_manager_v2_5 import ConfigManagerV2_5

# Unified AI Intelligence System support
try:
    from core_analytics_engine.unified_ai_intelligence_system_v2_5 import (
        UnifiedAIIntelligenceSystemV2_5,
        get_unified_ai_intelligence_system
    )
    UNIFIED_AI_AVAILABLE = True
except ImportError:
    UNIFIED_AI_AVAILABLE = False

# Pydantic AI Response Models
class MarketAnalysis(BaseModel):
    """AI-generated market analysis with reasoning."""
    market_sentiment: str = Field(description="Overall market sentiment (Bullish/Bearish/Neutral)")
    volatility_regime: str = Field(description="Current volatility environment")
    key_risks: List[str] = Field(description="Primary risks identified")
    opportunities: List[str] = Field(description="Trading opportunities identified")
    confidence_score: float = Field(description="AI confidence in analysis (0-1)")
    reasoning: str = Field(description="Detailed reasoning behind the analysis")

class StrategyRecommendation(BaseModel):
    """AI-enhanced strategy recommendation."""
    strategy_type: str = Field(description="Recommended options strategy")
    conviction_level: str = Field(description="High/Medium/Low conviction")
    conviction_score: float = Field(description="Numerical conviction (0-1)")
    entry_timing: str = Field(description="Optimal entry timing")
    risk_factors: List[str] = Field(description="Key risks for this strategy")
    profit_targets: List[str] = Field(description="Profit target levels")
    stop_loss_guidance: str = Field(description="Stop loss recommendations")
    market_context: str = Field(description="Why this strategy fits current market")
    reasoning: str = Field(description="Detailed AI reasoning")

class AdaptiveLearning(BaseModel):
    """AI learning from trade outcomes."""
    pattern_identified: str = Field(description="Market pattern learned")
    success_factors: List[str] = Field(description="Factors that led to success")
    failure_factors: List[str] = Field(description="Factors that led to failure")
    adjustment_recommendations: List[str] = Field(description="Recommended system adjustments")
    confidence_in_learning: float = Field(description="Confidence in the learning (0-1)")

class PydanticAIATIFEngineV2_5:
    """
    Pydantic AI-enhanced ATIF engine that provides intelligent, adaptive,
    and self-improving trading recommendations.
    """
    
    def __init__(self, config_manager: ConfigManagerV2_5, db_manager=None, mcp_db_path: str = "uber_elite.db"):
        self.logger = logging.getLogger(__name__)
        self.config_manager = config_manager
        self.db_manager = db_manager

        # Initialize unified AI intelligence system
        self.unified_ai_system = None
        if UNIFIED_AI_AVAILABLE and db_manager:
            try:
                self.unified_ai_system = get_unified_ai_intelligence_system(config_manager, db_manager)
                self.logger.info("🚀 Unified AI Intelligence System initialized")
            except Exception as e:
                self.logger.warning(f"Failed to initialize unified AI system: {e}")

        # Initialize Pydantic AI agents
        self._init_ai_agents()

        # Learning database for continuous improvement
        self.learning_history = []
        
    def _init_ai_agents(self):
        """Initialize specialized AI agents for different tasks."""
        
        # Market Analysis Agent
        self.market_analyst = Agent(
            model=OpenAIModel('gpt-4o'),
            result_type=MarketAnalysis,
            system_prompt="""
            You are an elite options trading market analyst with deep expertise in:
            - Options flow analysis and institutional behavior
            - Volatility regimes and gamma positioning
            - Market microstructure and dealer hedging
            - Risk management and position sizing
            
            Analyze the provided market data and generate insights that would be valuable
            for an institutional options trader. Focus on actionable intelligence.
            """
        )
        
        # Strategy Recommendation Agent
        self.strategy_advisor = Agent(
            model=OpenAIModel('gpt-4o'),
            result_type=StrategyRecommendation,
            system_prompt="""
            You are an expert options strategy advisor specializing in:
            - Complex options strategies and their optimal applications
            - Risk/reward optimization for different market conditions
            - Timing and execution of options trades
            - Portfolio hedging and risk management
            
            Given market analysis and current conditions, recommend the optimal
            options strategy with detailed reasoning and risk management guidance.
            """
        )
        
        # Adaptive Learning Agent
        self.learning_agent = Agent(
            model=OpenAIModel('gpt-4o'),
            result_type=AdaptiveLearning,
            system_prompt="""
            You are a machine learning specialist focused on trading system improvement.
            Analyze trade outcomes and market patterns to identify:
            - What factors led to successful trades
            - What factors led to unsuccessful trades
            - How the system can be improved
            - What patterns should be emphasized or avoided
            
            Provide actionable recommendations for system enhancement.
            """
        )
    
    async def generate_enhanced_analysis(
        self, 
        symbol: str,
        processed_data: ProcessedDataBundleV2_5,
        historical_performance: Optional[Dict[str, Any]] = None
    ) -> MarketAnalysis:
        """Generate AI-enhanced market analysis."""
        
        try:
            # Prepare market data for AI analysis
            market_data = self._prepare_market_data(symbol, processed_data, historical_performance)

            # Get enhanced context from multi-database if available
            enhanced_context = ""
            if self.multi_db_manager:
                try:
                    ai_patterns = await self.multi_db_manager.get_ai_learning_patterns(symbol)
                    cross_market = await self.multi_db_manager.get_cross_market_insights(symbol)

                    if ai_patterns:
                        enhanced_context += f"\n\nAI LEARNING PATTERNS:\n"
                        for pattern in ai_patterns[:3]:
                            enhanced_context += f"- {pattern['pattern_name']}: {pattern['success_rate']:.1%} success rate\n"

                    if cross_market:
                        enhanced_context += f"\nCROSS-MARKET CORRELATIONS:\n"
                        for corr in cross_market[:2]:
                            enhanced_context += f"- {corr['correlated_symbol']}: {corr['correlation_coefficient']:.2f} correlation\n"

                except Exception as e:
                    self.logger.warning(f"Failed to get enhanced context: {e}")

            # Get AI analysis with enhanced context
            analysis = await self.market_analyst.run(
                f"""
                Analyze the following market data for {symbol}:
                
                FLOW METRICS:
                - VAPI-FA: {market_data.get('vapi_fa', 'N/A')} (Flow acceleration)
                - DWFD: {market_data.get('dwfd', 'N/A')} (Smart money divergence)
                - TW-LAF: {market_data.get('tw_laf', 'N/A')} (Trend sustainability)
                
                GAMMA METRICS:
                - GIB: {market_data.get('gib', 'N/A')} (Dealer gamma positioning)
                - Current Price: ${market_data.get('price', 'N/A')}
                - Volatility: {market_data.get('volatility', 'N/A')}
                
                MARKET REGIME: {market_data.get('regime', 'Unknown')}
                
                HISTORICAL PERFORMANCE: {market_data.get('performance_summary', 'No data')}
                {enhanced_context}

                Provide a comprehensive analysis focusing on actionable trading insights.
                Consider the AI learning patterns and cross-market correlations in your analysis.
                """
            )
            
            # Store analysis for learning
            self._store_analysis_for_learning(symbol, analysis, market_data)
            
            return analysis.data
            
        except Exception as e:
            self.logger.error(f"Failed to generate AI analysis: {e}")
            # Return fallback analysis
            return MarketAnalysis(
                market_sentiment="Neutral",
                volatility_regime="Unknown",
                key_risks=["AI analysis unavailable"],
                opportunities=["Manual analysis required"],
                confidence_score=0.0,
                reasoning="AI analysis failed, manual review needed"
            )
    
    async def generate_enhanced_strategy(
        self,
        symbol: str,
        market_analysis: MarketAnalysis,
        processed_data: ProcessedDataBundleV2_5,
        current_atif_recommendations: List[ATIFStrategyDirectivePayloadV2_5]
    ) -> StrategyRecommendation:
        """Generate AI-enhanced strategy recommendation."""
        
        try:
            # Prepare strategy context
            strategy_context = self._prepare_strategy_context(
                symbol, market_analysis, processed_data, current_atif_recommendations
            )
            
            # Get AI strategy recommendation
            strategy = await self.strategy_advisor.run(
                f"""
                Based on the following market analysis and current conditions for {symbol}:
                
                MARKET ANALYSIS:
                - Sentiment: {market_analysis.market_sentiment}
                - Volatility Regime: {market_analysis.volatility_regime}
                - Key Risks: {', '.join(market_analysis.key_risks)}
                - Opportunities: {', '.join(market_analysis.opportunities)}
                - AI Confidence: {market_analysis.confidence_score:.2f}
                
                CURRENT ATIF RECOMMENDATIONS: {strategy_context.get('current_recs', 'None')}
                
                MARKET CONDITIONS:
                {strategy_context.get('conditions', 'Standard conditions')}
                
                Recommend the optimal options strategy with detailed execution guidance.
                Consider risk management, timing, and current market microstructure.
                """
            )
            
            return strategy.data
            
        except Exception as e:
            self.logger.error(f"Failed to generate AI strategy: {e}")
            # Return fallback strategy
            return StrategyRecommendation(
                strategy_type="Hold/Wait",
                conviction_level="Low",
                conviction_score=0.0,
                entry_timing="Wait for clearer signals",
                risk_factors=["AI strategy generation failed"],
                profit_targets=["Manual analysis required"],
                stop_loss_guidance="Standard risk management",
                market_context="AI unavailable",
                reasoning="AI strategy generation failed, manual review needed"
            )
    
    async def learn_from_outcome(
        self,
        trade_outcome: Dict[str, Any],
        market_conditions_at_entry: Dict[str, Any],
        strategy_used: str
    ) -> AdaptiveLearning:
        """Learn from trade outcomes to improve future recommendations."""
        
        try:
            # Prepare learning context
            learning_context = {
                'outcome': trade_outcome,
                'market_conditions': market_conditions_at_entry,
                'strategy': strategy_used
            }
            
            # Get AI learning insights
            learning = await self.learning_agent.run(
                f"""
                Analyze this trade outcome to improve future recommendations:
                
                TRADE OUTCOME:
                - Strategy: {strategy_used}
                - Result: {trade_outcome.get('result', 'Unknown')}
                - P&L: {trade_outcome.get('pnl', 'Unknown')}
                - Duration: {trade_outcome.get('duration', 'Unknown')}
                
                MARKET CONDITIONS AT ENTRY:
                - VAPI-FA: {market_conditions_at_entry.get('vapi_fa', 'N/A')}
                - DWFD: {market_conditions_at_entry.get('dwfd', 'N/A')}
                - TW-LAF: {market_conditions_at_entry.get('tw_laf', 'N/A')}
                - GIB: {market_conditions_at_entry.get('gib', 'N/A')}
                - Regime: {market_conditions_at_entry.get('regime', 'Unknown')}
                
                What patterns can be learned? How should the system be improved?
                """
            )
            
            # Store learning for future use
            self.learning_history.append({
                'timestamp': datetime.now(),
                'learning': learning.data,
                'context': learning_context
            })

            # Store learning pattern in multi-database if available
            if self.multi_db_manager and learning.data.confidence_in_learning > 0.7:
                try:
                    pattern_data = {
                        'pattern_name': learning.data.pattern_identified,
                        'pattern_description': f"Learned from {strategy_used} trade outcome",
                        'market_conditions': market_conditions_at_entry,
                        'success_rate': 1.0 if trade_outcome.get('result') == 'WIN' else 0.0,
                        'confidence_score': learning.data.confidence_in_learning,
                        'sample_size': 1
                    }
                    await self.multi_db_manager.store_ai_learning_pattern(pattern_data)
                except Exception as e:
                    self.logger.error(f"Failed to store learning pattern: {e}")

            # Apply learning to system if confidence is high
            if learning.data.confidence_in_learning > 0.8:
                await self._apply_learning(learning.data)
            
            return learning.data
            
        except Exception as e:
            self.logger.error(f"Failed to learn from outcome: {e}")
            return AdaptiveLearning(
                pattern_identified="Learning failed",
                success_factors=[],
                failure_factors=["AI learning unavailable"],
                adjustment_recommendations=["Manual review required"],
                confidence_in_learning=0.0
            )
    
    def _prepare_market_data(self, symbol: str, processed_data: ProcessedDataBundleV2_5, historical_performance: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Prepare market data for AI analysis."""
        und_data = processed_data.underlying_data_enriched
        
        return {
            'symbol': symbol,
            'price': getattr(und_data, 'price', None),
            'vapi_fa': getattr(und_data, 'vapi_fa_z_score_und', None),
            'dwfd': getattr(und_data, 'dwfd_z_score_und', None),
            'tw_laf': getattr(und_data, 'tw_laf_z_score_und', None),
            'gib': getattr(und_data, 'gib_oi_based_und', None),
            'volatility': getattr(und_data, 'u_volatility', None),
            'regime': getattr(processed_data, 'market_regime_summary', 'Unknown'),
            'performance_summary': self._format_performance_summary(historical_performance) if historical_performance else 'No historical data'
        }
    
    def _prepare_strategy_context(self, symbol: str, analysis: MarketAnalysis, processed_data: ProcessedDataBundleV2_5, current_recs: List) -> Dict[str, Any]:
        """Prepare strategy context for AI recommendation."""
        return {
            'symbol': symbol,
            'current_recs': [rec.selected_strategy_type for rec in current_recs] if current_recs else 'None',
            'conditions': f"Price: ${getattr(processed_data.underlying_data_enriched, 'price', 'N/A')}, "
                         f"Regime: {getattr(processed_data, 'market_regime_summary', 'Unknown')}"
        }
    
    def _format_performance_summary(self, performance: Dict[str, Any]) -> str:
        """Format performance data for AI consumption."""
        return f"Win Rate: {performance.get('win_rate', 0):.1%}, Total Trades: {performance.get('total_trades', 0)}"
    
    def _store_analysis_for_learning(self, symbol: str, analysis: MarketAnalysis, market_data: Dict[str, Any]):
        """Store analysis for future learning."""
        if self.db_manager:
            try:
                learning_data = {
                    'symbol': symbol,
                    'analysis': analysis.model_dump(),
                    'market_data': market_data,
                    'timestamp': datetime.now()
                }
                # Store in database for learning
                # self.db_manager.store_ai_learning_data(learning_data)
            except Exception as e:
                self.logger.error(f"Failed to store learning data: {e}")
    
    async def _apply_learning(self, learning: AdaptiveLearning):
        """Apply high-confidence learning to improve the system."""
        # This would update system parameters, thresholds, etc.
        # based on AI learning insights
        self.logger.info(f"Applying AI learning: {learning.pattern_identified}")
        
        # Example: Adjust thresholds based on learning
        for adjustment in learning.adjustment_recommendations:
            self.logger.info(f"AI Recommendation: {adjustment}")
