from pydantic import BaseModel, Field, FilePath
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
import pandas as pd # Will be used where DataFrames are unavoidable

# Placeholder for pandas DataFrame if we decide to type hint it (not true Pydantic validation for content)
PandasDataFrame = Any # Or pd.DataFrame, but then Pydantic won't validate its contents


# --- Canonical Parameter Lists from ConvexValue ---
# For reference and ensuring Raw models are comprehensive.
UNDERLYING_REQUIRED_PARAMS_CV = [
    "price", "volatility", "day_volume", "call_gxoi", "put_gxoi",
    "gammas_call_buy", "gammas_call_sell", "gammas_put_buy", "gammas_put_sell",
    "deltas_call_buy", "deltas_call_sell", "deltas_put_buy", "deltas_put_sell",
    "vegas_call_buy", "vegas_call_sell", "vegas_put_buy", "vegas_put_sell",
    "thetas_call_buy", "thetas_call_sell", "thetas_put_buy", "thetas_put_sell",
    "call_vxoi", "put_vxoi", "value_bs", "volm_bs", "deltas_buy", "deltas_sell",
    "vegas_buy", "vegas_sell", "thetas_buy", "thetas_sell", "volm_call_buy",
    "volm_put_buy", "volm_call_sell", "volm_put_sell", "value_call_buy",
    "value_put_buy", "value_call_sell", "value_put_sell", "vflowratio",
    "dxoi", "gxoi", "vxoi", "txoi", "call_dxoi", "put_dxoi"
]

OPTIONS_CHAIN_REQUIRED_PARAMS_CV = [
    "price", "volatility", "multiplier", "oi", "delta", "gamma", "theta", "vega",
    "vanna", "vomma", "charm", "dxoi", "gxoi", "vxoi", "txoi", "vannaxoi",
    "vommaxoi", "charmxoi", "dxvolm", "gxvolm", "vxvolm", "txvolm", "vannaxvolm",
    "vommaxvolm", "charmxvolm", "value_bs", "volm_bs", "deltas_buy", "deltas_sell",
    "gammas_buy", "gammas_sell", "vegas_buy", "vegas_sell", "thetas_buy", "thetas_sell",
    "valuebs_5m", "volmbs_5m", "valuebs_15m", "volmbs_15m",
    "valuebs_30m", "volmbs_30m", "valuebs_60m", "volmbs_60m",
    "volm", "volm_buy", "volm_sell", "value_buy", "value_sell"
]
# End Canonical Parameter Lists


class RawOptionsContractV2_5(BaseModel):
    contract_symbol: str
    strike: float
    opt_kind: str # "call" or "put"
    dte_calc: float # Calculated DTE

    # Fields corresponding to OPTIONS_CHAIN_REQUIRED_PARAMS_CV
    # Existing fields (comments denote mapping if name differs)
    open_interest: Optional[float] = Field(None, description="Open interest for the contract (maps to CV 'oi')")
    iv: Optional[float] = Field(None, description="Implied Volatility for the contract (maps to CV 'volatility')")
    raw_price: Optional[float] = Field(None, description="Raw price of the option contract from CV (maps to CV 'price')") # Explicit for CV 'price'
    delta_contract: Optional[float] = Field(None, description="Delta per contract (maps to CV 'delta')")
    gamma_contract: Optional[float] = Field(None, description="Gamma per contract (maps to CV 'gamma')")
    theta_contract: Optional[float] = Field(None, description="Theta per contract (maps to CV 'theta')")
    vega_contract: Optional[float] = Field(None, description="Vega per contract (maps to CV 'vega')")
    rho_contract: Optional[float] = Field(None, description="Rho per contract") # Rho is standard, not explicitly in CV list but good to have
    vanna_contract: Optional[float] = Field(None, description="Vanna per contract (maps to CV 'vanna')")
    vomma_contract: Optional[float] = Field(None, description="Vomma per contract (maps to CV 'vomma')")
    charm_contract: Optional[float] = Field(None, description="Charm per contract (maps to CV 'charm')")

    # Greeks OI (Open Interest based Greeks, if provided directly)
    dxoi: Optional[float] = Field(None, description="Field from ConvexValue OPTIONS_CHAIN_REQUIRED_PARAMS_CV (dxoi)")
    gxoi: Optional[float] = Field(None, description="Field from ConvexValue OPTIONS_CHAIN_REQUIRED_PARAMS_CV (gxoi)")
    vxoi: Optional[float] = Field(None, description="Field from ConvexValue OPTIONS_CHAIN_REQUIRED_PARAMS_CV (vxoi)")
    txoi: Optional[float] = Field(None, description="Field from ConvexValue OPTIONS_CHAIN_REQUIRED_PARAMS_CV (txoi)")
    vannaxoi: Optional[float] = Field(None, description="Field from ConvexValue OPTIONS_CHAIN_REQUIRED_PARAMS_CV (vannaxoi)")
    vommaxoi: Optional[float] = Field(None, description="Field from ConvexValue OPTIONS_CHAIN_REQUIRED_PARAMS_CV (vommaxoi)")
    charmxoi: Optional[float] = Field(None, description="Field from ConvexValue OPTIONS_CHAIN_REQUIRED_PARAMS_CV (charmxoi)")

    # Greek-Volume Proxies (some may be redundant if direct signed Greek flows are available)
    dxvolm: Optional[Any] = Field(None, description="Field from ConvexValue OPTIONS_CHAIN_REQUIRED_PARAMS_CV (dxvolm)")
    gxvolm: Optional[Any] = Field(None, description="Field from ConvexValue OPTIONS_CHAIN_REQUIRED_PARAMS_CV (gxvolm)")
    vxvolm: Optional[Any] = Field(None, description="Field from ConvexValue OPTIONS_CHAIN_REQUIRED_PARAMS_CV (vxvolm)")
    txvolm: Optional[Any] = Field(None, description="Field from ConvexValue OPTIONS_CHAIN_REQUIRED_PARAMS_CV (txvolm)")
    vannaxvolm: Optional[Any] = Field(None, description="Field from ConvexValue OPTIONS_CHAIN_REQUIRED_PARAMS_CV (vannaxvolm)")
    vommaxvolm: Optional[Any] = Field(None, description="Field from ConvexValue OPTIONS_CHAIN_REQUIRED_PARAMS_CV (vommaxvolm)")
    charmxvolm: Optional[Any] = Field(None, description="Field from ConvexValue OPTIONS_CHAIN_REQUIRED_PARAMS_CV (charmxvolm)")

    # Transaction Data
    value_bs: Optional[float] = Field(None, description="Day Sum of Buy Value minus Sell Value Traded (maps to CV 'value_bs')")
    volm_bs: Optional[float] = Field(None, description="Volume of Buys minus Sells (maps to CV 'volm_bs')")
    volm: Optional[float] = Field(None, description="Total volume for the contract (maps to CV 'volm')")

    # Rolling Flows
    valuebs_5m: Optional[float] = Field(None, description="Field from ConvexValue OPTIONS_CHAIN_REQUIRED_PARAMS_CV (valuebs_5m)")
    volmbs_5m: Optional[float] = Field(None, description="Field from ConvexValue OPTIONS_CHAIN_REQUIRED_PARAMS_CV (volmbs_5m)")
    valuebs_15m: Optional[float] = Field(None, description="Field from ConvexValue OPTIONS_CHAIN_REQUIRED_PARAMS_CV (valuebs_15m)")
    volmbs_15m: Optional[float] = Field(None, description="Field from ConvexValue OPTIONS_CHAIN_REQUIRED_PARAMS_CV (volmbs_15m)")
    valuebs_30m: Optional[float] = Field(None, description="Field from ConvexValue OPTIONS_CHAIN_REQUIRED_PARAMS_CV (valuebs_30m)")
    volmbs_30m: Optional[float] = Field(None, description="Field from ConvexValue OPTIONS_CHAIN_REQUIRED_PARAMS_CV (volmbs_30m)")
    valuebs_60m: Optional[float] = Field(None, description="Field from ConvexValue OPTIONS_CHAIN_REQUIRED_PARAMS_CV (valuebs_60m)")
    volmbs_60m: Optional[float] = Field(None, description="Field from ConvexValue OPTIONS_CHAIN_REQUIRED_PARAMS_CV (volmbs_60m)")

    # Bid/Ask for liquidity calculations
    bid_price: Optional[float] = Field(None, description="Bid price of the option")
    ask_price: Optional[float] = Field(None, description="Ask price of the option")
    mid_price: Optional[float] = Field(None, description="Midpoint price of the option")

    # New fields from OPTIONS_CHAIN_REQUIRED_PARAMS_CV
    multiplier: Optional[float] = Field(None, description="Option contract multiplier (e.g., 100) (maps to CV 'multiplier')")
    deltas_buy: Optional[Any] = Field(None, description="Field from ConvexValue OPTIONS_CHAIN_REQUIRED_PARAMS_CV (deltas_buy)")
    deltas_sell: Optional[Any] = Field(None, description="Field from ConvexValue OPTIONS_CHAIN_REQUIRED_PARAMS_CV (deltas_sell)")
    gammas_buy: Optional[Any] = Field(None, description="Field from ConvexValue OPTIONS_CHAIN_REQUIRED_PARAMS_CV (gammas_buy)")
    gammas_sell: Optional[Any] = Field(None, description="Field from ConvexValue OPTIONS_CHAIN_REQUIRED_PARAMS_CV (gammas_sell)")
    vegas_buy: Optional[Any] = Field(None, description="Field from ConvexValue OPTIONS_CHAIN_REQUIRED_PARAMS_CV (vegas_buy)")
    vegas_sell: Optional[Any] = Field(None, description="Field from ConvexValue OPTIONS_CHAIN_REQUIRED_PARAMS_CV (vegas_sell)")
    thetas_buy: Optional[Any] = Field(None, description="Field from ConvexValue OPTIONS_CHAIN_REQUIRED_PARAMS_CV (thetas_buy)")
    thetas_sell: Optional[Any] = Field(None, description="Field from ConvexValue OPTIONS_CHAIN_REQUIRED_PARAMS_CV (thetas_sell)")
    volm_buy: Optional[Any] = Field(None, description="Field from ConvexValue OPTIONS_CHAIN_REQUIRED_PARAMS_CV (volm_buy)")
    volm_sell: Optional[Any] = Field(None, description="Field from ConvexValue OPTIONS_CHAIN_REQUIRED_PARAMS_CV (volm_sell)")
    value_buy: Optional[Any] = Field(None, description="Field from ConvexValue OPTIONS_CHAIN_REQUIRED_PARAMS_CV (value_buy)")
    value_sell: Optional[Any] = Field(None, description="Field from ConvexValue OPTIONS_CHAIN_REQUIRED_PARAMS_CV (value_sell)")

    class Config:
        extra = 'allow'

class RawUnderlyingDataV2_5(BaseModel):
    symbol: str
    timestamp: datetime
    price: Optional[float] = Field(None, description="Current underlying price (maps to CV 'price')")
    price_change_abs_und: Optional[float] = Field(None, description="Absolute price change of the underlying")
    price_change_pct_und: Optional[float] = Field(None, description="Percentage price change of the underlying")

    # Tradier specific daily OHLC (can also come from other sources if CV doesn't provide all)
    day_open_price_und: Optional[float] = Field(None, description="Daily open price from primary source (e.g. Tradier)")
    day_high_price_und: Optional[float] = Field(None, description="Daily high price from primary source")
    day_low_price_und: Optional[float] = Field(None, description="Daily low price from primary source")
    prev_day_close_price_und: Optional[float] = Field(None, description="Previous day's closing price from primary source")

    # Fields from UNDERLYING_REQUIRED_PARAMS_CV
    u_volatility: Optional[float] = Field(None, description="General IV for the underlying (maps to CV 'volatility')")
    day_volume: Optional[Any] = Field(None, description="Field from ConvexValue UNDERLYING_REQUIRED_PARAMS_CV (day_volume)")
    call_gxoi: Optional[Any] = Field(None, description="Field from ConvexValue UNDERLYING_REQUIRED_PARAMS_CV (call_gxoi)")
    put_gxoi: Optional[Any] = Field(None, description="Field from ConvexValue UNDERLYING_REQUIRED_PARAMS_CV (put_gxoi)")
    gammas_call_buy: Optional[Any] = Field(None, description="Field from ConvexValue UNDERLYING_REQUIRED_PARAMS_CV (gammas_call_buy)")
    gammas_call_sell: Optional[Any] = Field(None, description="Field from ConvexValue UNDERLYING_REQUIRED_PARAMS_CV (gammas_call_sell)")
    gammas_put_buy: Optional[Any] = Field(None, description="Field from ConvexValue UNDERLYING_REQUIRED_PARAMS_CV (gammas_put_buy)")
    gammas_put_sell: Optional[Any] = Field(None, description="Field from ConvexValue UNDERLYING_REQUIRED_PARAMS_CV (gammas_put_sell)")
    deltas_call_buy: Optional[Any] = Field(None, description="Field from ConvexValue UNDERLYING_REQUIRED_PARAMS_CV (deltas_call_buy)")
    deltas_call_sell: Optional[Any] = Field(None, description="Field from ConvexValue UNDERLYING_REQUIRED_PARAMS_CV (deltas_call_sell)")
    deltas_put_buy: Optional[Any] = Field(None, description="Field from ConvexValue UNDERLYING_REQUIRED_PARAMS_CV (deltas_put_buy)")
    deltas_put_sell: Optional[Any] = Field(None, description="Field from ConvexValue UNDERLYING_REQUIRED_PARAMS_CV (deltas_put_sell)")
    vegas_call_buy: Optional[Any] = Field(None, description="Field from ConvexValue UNDERLYING_REQUIRED_PARAMS_CV (vegas_call_buy)")
    vegas_call_sell: Optional[Any] = Field(None, description="Field from ConvexValue UNDERLYING_REQUIRED_PARAMS_CV (vegas_call_sell)")
    vegas_put_buy: Optional[Any] = Field(None, description="Field from ConvexValue UNDERLYING_REQUIRED_PARAMS_CV (vegas_put_buy)")
    vegas_put_sell: Optional[Any] = Field(None, description="Field from ConvexValue UNDERLYING_REQUIRED_PARAMS_CV (vegas_put_sell)")
    thetas_call_buy: Optional[Any] = Field(None, description="Field from ConvexValue UNDERLYING_REQUIRED_PARAMS_CV (thetas_call_buy)")
    thetas_call_sell: Optional[Any] = Field(None, description="Field from ConvexValue UNDERLYING_REQUIRED_PARAMS_CV (thetas_call_sell)")
    thetas_put_buy: Optional[Any] = Field(None, description="Field from ConvexValue UNDERLYING_REQUIRED_PARAMS_CV (thetas_put_buy)")
    thetas_put_sell: Optional[Any] = Field(None, description="Field from ConvexValue UNDERLYING_REQUIRED_PARAMS_CV (thetas_put_sell)")
    call_vxoi: Optional[Any] = Field(None, description="Field from ConvexValue UNDERLYING_REQUIRED_PARAMS_CV (call_vxoi)")
    put_vxoi: Optional[Any] = Field(None, description="Field from ConvexValue UNDERLYING_REQUIRED_PARAMS_CV (put_vxoi)")
    value_bs: Optional[Any] = Field(None, description="Overall value buy/sell for underlying (maps to CV 'value_bs')")
    volm_bs: Optional[Any] = Field(None, description="Overall volume buy/sell for underlying (maps to CV 'volm_bs')")
    deltas_buy: Optional[Any] = Field(None, description="Overall delta buy for underlying (maps to CV 'deltas_buy')")
    deltas_sell: Optional[Any] = Field(None, description="Overall delta sell for underlying (maps to CV 'deltas_sell')")
    vegas_buy: Optional[Any] = Field(None, description="Overall vega buy for underlying (maps to CV 'vegas_buy')")
    vegas_sell: Optional[Any] = Field(None, description="Overall vega sell for underlying (maps to CV 'vegas_sell')")
    thetas_buy: Optional[Any] = Field(None, description="Overall theta buy for underlying (maps to CV 'thetas_buy')")
    thetas_sell: Optional[Any] = Field(None, description="Overall theta sell for underlying (maps to CV 'thetas_sell')")
    volm_call_buy: Optional[Any] = Field(None, description="Field from ConvexValue UNDERLYING_REQUIRED_PARAMS_CV (volm_call_buy)")
    volm_put_buy: Optional[Any] = Field(None, description="Field from ConvexValue UNDERLYING_REQUIRED_PARAMS_CV (volm_put_buy)")
    volm_call_sell: Optional[Any] = Field(None, description="Field from ConvexValue UNDERLYING_REQUIRED_PARAMS_CV (volm_call_sell)")
    volm_put_sell: Optional[Any] = Field(None, description="Field from ConvexValue UNDERLYING_REQUIRED_PARAMS_CV (volm_put_sell)")
    value_call_buy: Optional[Any] = Field(None, description="Field from ConvexValue UNDERLYING_REQUIRED_PARAMS_CV (value_call_buy)")
    value_put_buy: Optional[Any] = Field(None, description="Field from ConvexValue UNDERLYING_REQUIRED_PARAMS_CV (value_put_buy)")
    value_call_sell: Optional[Any] = Field(None, description="Field from ConvexValue UNDERLYING_REQUIRED_PARAMS_CV (value_call_sell)")
    value_put_sell: Optional[Any] = Field(None, description="Field from ConvexValue UNDERLYING_REQUIRED_PARAMS_CV (value_put_sell)")
    vflowratio: Optional[Any] = Field(None, description="Field from ConvexValue UNDERLYING_REQUIRED_PARAMS_CV (vflowratio)")
    dxoi: Optional[Any] = Field(None, description="Overall dxoi for underlying (maps to CV 'dxoi')")
    gxoi: Optional[Any] = Field(None, description="Overall gxoi for underlying (maps to CV 'gxoi')")
    vxoi: Optional[Any] = Field(None, description="Overall vxoi for underlying (maps to CV 'vxoi')")
    txoi: Optional[Any] = Field(None, description="Overall txoi for underlying (maps to CV 'txoi')")
    call_dxoi: Optional[Any] = Field(None, description="Field from ConvexValue UNDERLYING_REQUIRED_PARAMS_CV (call_dxoi)")
    put_dxoi: Optional[Any] = Field(None, description="Field from ConvexValue UNDERLYING_REQUIRED_PARAMS_CV (put_dxoi)")

    # Other existing fields
    tradier_iv5_approx_smv_avg: Optional[float] = Field(None, description="Tradier IV5 approximation")
    total_call_oi_und: Optional[float] = Field(None, description="Total call OI for the underlying")
    total_put_oi_und: Optional[float] = Field(None, description="Total put OI for the underlying")
    total_call_vol_und: Optional[float] = Field(None, description="Total call volume for the underlying")
    total_put_vol_und: Optional[float] = Field(None, description="Total put volume for the underlying")

    class Config:
        extra = 'allow'

class RawUnderlyingDataCombinedV2_5(RawUnderlyingDataV2_5):
    tradier_open: Optional[float] = Field(None, description="Tradier daily open price")
    tradier_high: Optional[float] = Field(None, description="Tradier daily high price")
    tradier_low: Optional[float] = Field(None, description="Tradier daily low price")
    tradier_close: Optional[float] = Field(None, description="Tradier daily close price")
    tradier_volume: Optional[float] = Field(None, description="Tradier daily volume")
    tradier_vwap: Optional[float] = Field(None, description="Tradier daily VWAP")

    class Config:
        extra = 'allow'

class UnprocessedDataBundleV2_5(BaseModel):
    options_contracts: List[RawOptionsContractV2_5] = Field(default_factory=list)
    underlying_data: RawUnderlyingDataCombinedV2_5
    fetch_timestamp: datetime
    errors: List[str] = Field(default_factory=list)

class ProcessedContractMetricsV2_5(RawOptionsContractV2_5):
    vri_0dte_contract: Optional[float] = None
    vfi_0dte_contract: Optional[float] = None
    vvr_0dte_contract: Optional[float] = None

class ProcessedStrikeLevelMetricsV2_5(BaseModel):
    strike: float
    total_dxoi_at_strike: Optional[float] = None
    total_gxoi_at_strike: Optional[float] = None
    total_vxoi_at_strike: Optional[float] = None
    total_txoi_at_strike: Optional[float] = None
    total_charmxoi_at_strike: Optional[float] = None
    total_vannaxoi_at_strike: Optional[float] = None
    total_vommaxoi_at_strike: Optional[float] = None
    net_cust_delta_flow_at_strike: Optional[float] = None
    net_cust_gamma_flow_at_strike: Optional[float] = None
    net_cust_vega_flow_at_strike: Optional[float] = None
    net_cust_theta_flow_at_strike: Optional[float] = None
    net_cust_charm_flow_proxy_at_strike: Optional[float] = None
    net_cust_vanna_flow_proxy_at_strike: Optional[float] = None
    nvp_at_strike: Optional[float] = None
    nvp_vol_at_strike: Optional[float] = None
    a_dag_strike: Optional[float] = None
    a_dag_exposure: Optional[float] = None
    a_dag_adaptive_alpha: Optional[float] = None
    a_dag_flow_alignment: Optional[float] = None
    a_dag_directional_multiplier: Optional[float] = None
    e_sdag_mult_strike: Optional[float] = None
    e_sdag_dir_strike: Optional[float] = None
    e_sdag_w_strike: Optional[float] = None
    e_sdag_vf_strike: Optional[float] = None
    e_sdag_adaptive_delta_weight_mult: Optional[float] = None
    e_sdag_adaptive_delta_weight_dir: Optional[float] = None
    e_sdag_adaptive_delta_weight_w: Optional[float] = None
    e_sdag_adaptive_delta_weight_vf: Optional[float] = None
    d_tdpi_strike: Optional[float] = None
    e_ctr_strike: Optional[float] = None
    e_tdfi_strike: Optional[float] = None
    vri_2_0_strike: Optional[float] = None
    a_mspi_strike: Optional[float] = None
    a_mspi_a_dag_component: Optional[float] = None
    a_mspi_d_tdpi_component: Optional[float] = None
    a_mspi_vri_2_0_component: Optional[float] = None
    a_mspi_e_sdag_mult_component: Optional[float] = None
    a_mspi_e_sdag_dir_component: Optional[float] = None
    a_sai_und_avg: Optional[float] = None
    a_ssi_und_avg: Optional[float] = None
    e_vvr_sens_strike: Optional[float] = None
    e_vfi_sens_strike: Optional[float] = None
    arfi_strike: Optional[float] = None
    sgdhp_score_strike: Optional[float] = None
    ugch_score_strike: Optional[float] = None
    vri_0dte: Optional[float] = None
    vfi_0dte: Optional[float] = None
    vvr_0dte: Optional[float] = None
    vci_0dte: Optional[float] = None
    gci_0dte: Optional[float] = None
    dci_0dte: Optional[float] = None
    gci_strike: Optional[float] = None  # Gamma Concentration Index at strike level
    dci_strike: Optional[float] = None  # Delta Concentration Index at strike level
    class Config:
        extra = 'allow'

class ProcessedUnderlyingAggregatesV2_5(RawUnderlyingDataCombinedV2_5):
    gib_oi_based_und: Optional[float] = None
    td_gib_und: Optional[float] = None
    hp_eod_und: Optional[float] = None
    net_cust_delta_flow_und: Optional[float] = None
    net_cust_gamma_flow_und: Optional[float] = None
    net_cust_vega_flow_und: Optional[float] = None
    net_cust_theta_flow_und: Optional[float] = None
    net_value_flow_5m_und: Optional[float] = None
    net_vol_flow_5m_und: Optional[float] = None
    net_value_flow_15m_und: Optional[float] = None
    net_vol_flow_15m_und: Optional[float] = None
    net_value_flow_30m_und: Optional[float] = None
    net_vol_flow_30m_und: Optional[float] = None
    net_value_flow_60m_und: Optional[float] = None
    net_vol_flow_60m_und: Optional[float] = None
    vri_0dte_und_sum: Optional[float] = None
    vfi_0dte_und_sum: Optional[float] = None
    vvr_0dte_und_avg: Optional[float] = None
    vci_0dte_agg: Optional[float] = None
    arfi_overall_und_avg: Optional[float] = None
    a_mspi_und_summary_score: Optional[float] = None
    a_sai_und_avg: Optional[float] = None
    a_ssi_und_avg: Optional[float] = None
    vri_2_0_und_aggregate: Optional[float] = None
    vapi_fa_z_score_und: Optional[float] = None
    dwfd_z_score_und: Optional[float] = None
    tw_laf_z_score_und: Optional[float] = None
    ivsdh_surface_data: Optional[PandasDataFrame] = None
    current_market_regime_v2_5: Optional[str] = None
    ticker_context_dict_v2_5: Optional['TickerContextDictV2_5'] = None
    atr_und: Optional[float] = None
    dynamic_thresholds: Optional[Dict[str, Any]] = None
    gci_0dte_agg: Optional[float] = None  # Aggregate Gamma Concentration Index for 0DTE
    dci_0dte_agg: Optional[float] = None  # Aggregate Delta Concentration Index for 0DTE
    class Config:
        arbitrary_types_allowed = True
        extra = 'allow'

class ProcessedDataBundleV2_5(BaseModel):
    options_data_with_metrics: List[ProcessedContractMetricsV2_5] = Field(default_factory=list)
    strike_level_data_with_metrics: List[ProcessedStrikeLevelMetricsV2_5] = Field(default_factory=list)
    underlying_data_enriched: ProcessedUnderlyingAggregatesV2_5
    processing_timestamp: datetime
    errors: List[str] = Field(default_factory=list)

class TickerContextDictV2_5(BaseModel):
    is_0dte: Optional[bool] = None
    is_1dte: Optional[bool] = None
    is_spx_mwf_expiry_type: Optional[bool] = None
    is_spy_eom_expiry: Optional[bool] = None
    is_quad_witching_week_flag: Optional[bool] = None
    days_to_nearest_0dte: Optional[int] = None
    days_to_monthly_opex: Optional[int] = None
    is_fomc_meeting_day: Optional[bool] = None
    is_fomc_announcement_imminent: Optional[bool] = None
    post_fomc_drift_period_active: Optional[bool] = None
    vix_spy_price_divergence_strong_negative: Optional[bool] = None
    active_intraday_session: Optional[str] = None
    is_near_auction_period: Optional[bool] = None
    ticker_liquidity_profile_flag: Optional[str] = None
    ticker_volatility_state_flag: Optional[str] = None
    earnings_approaching_flag: Optional[bool] = None
    days_to_earnings: Optional[int] = None
    class Config:
        extra = 'allow'

class SignalPayloadV2_5(BaseModel):
    signal_id: str
    signal_name: str
    symbol: str
    timestamp: datetime
    signal_type: str
    direction: Optional[str] = None
    strength_score: float
    strike_impacted: Optional[float] = None
    regime_at_signal_generation: Optional[str] = None
    supporting_metrics: Dict[str, Any] = Field(default_factory=dict)

class KeyLevelV2_5(BaseModel):
    level_price: float
    level_type: str
    conviction_score: float
    contributing_metrics: List[str] = Field(default_factory=list)
    source_identifier: Optional[str] = None

class KeyLevelsDataV2_5(BaseModel):
    supports: List[KeyLevelV2_5] = Field(default_factory=list)
    resistances: List[KeyLevelV2_5] = Field(default_factory=list)
    pin_zones: List[KeyLevelV2_5] = Field(default_factory=list)
    vol_triggers: List[KeyLevelV2_5] = Field(default_factory=list)
    major_walls: List[KeyLevelV2_5] = Field(default_factory=list)
    timestamp: datetime

class ATIFSituationalAssessmentProfileV2_5(BaseModel):
    bullish_assessment_score: float = 0.0
    bearish_assessment_score: float = 0.0
    vol_expansion_score: float = 0.0
    vol_contraction_score: float = 0.0
    mean_reversion_likelihood: float = 0.0
    timestamp: datetime

class ATIFStrategyDirectivePayloadV2_5(BaseModel):
    selected_strategy_type: str
    target_dte_min: int
    target_dte_max: int
    target_delta_long_leg_min: Optional[float] = None
    target_delta_long_leg_max: Optional[float] = None
    target_delta_short_leg_min: Optional[float] = None
    target_delta_short_leg_max: Optional[float] = None
    underlying_price_at_decision: float
    final_conviction_score_from_atif: float
    supportive_rationale_components: Dict[str, Any] = Field(default_factory=dict)
    assessment_profile: ATIFSituationalAssessmentProfileV2_5

class ATIFManagementDirectiveV2_5(BaseModel):
    recommendation_id: str
    action: str
    reason: str
    new_stop_loss: Optional[float] = None
    new_target_1: Optional[float] = None
    new_target_2: Optional[float] = None
    exit_price_type: Optional[str] = None
    percentage_to_manage: Optional[float] = None

class TradeParametersV2_5(BaseModel):
    option_symbol: str
    option_type: str
    strike: float
    expiration_str: str
    entry_price_suggested: float
    stop_loss_price: float
    target_1_price: float
    target_2_price: Optional[float] = None
    target_3_price: Optional[float] = None
    target_rationale: str

class ActiveRecommendationPayloadV2_5(BaseModel):
    recommendation_id: str
    symbol: str
    timestamp_issued: datetime
    strategy_type: str
    selected_option_details: List[Dict[str, Any]]
    trade_bias: str
    entry_price_initial: float
    stop_loss_initial: float
    target_1_initial: float
    target_2_initial: Optional[float] = None
    target_3_initial: Optional[float] = None
    entry_price_actual: Optional[float] = None
    stop_loss_current: float
    target_1_current: float
    target_2_current: Optional[float] = None
    target_3_current: Optional[float] = None
    target_rationale: str
    status: str
    status_update_reason: Optional[str] = None
    atif_conviction_score_at_issuance: float
    triggering_signals_summary: Optional[str] = None
    regime_at_issuance: str
    exit_timestamp: Optional[datetime] = None
    exit_price: Optional[float] = None
    pnl_percentage: Optional[float] = None
    pnl_absolute: Optional[float] = None
    exit_reason: Optional[str] = None
    class Config:
        extra = 'allow'

# ===== AI PREDICTIONS MODELS =====

class AIPredictionV2_5(BaseModel):
    """Pydantic model for AI predictions with full validation."""
    id: Optional[int] = Field(None, description="Database ID (auto-generated)")
    symbol: str = Field(..., description="Trading symbol (e.g., 'SPY')")
    prediction_type: str = Field(..., description="Type of prediction ('price', 'direction', 'volatility', 'eots_direction')")
    prediction_value: Optional[float] = Field(None, description="Predicted numerical value")
    prediction_direction: str = Field(..., description="Predicted direction", pattern="^(UP|DOWN|NEUTRAL)$")
    confidence_score: float = Field(..., description="Confidence level (0.0 to 1.0)", ge=0.0, le=1.0)
    time_horizon: str = Field(..., description="Time frame ('1H', '4H', '1D', '1W', etc.)")
    prediction_timestamp: datetime = Field(default_factory=datetime.now, description="When prediction was made")
    target_timestamp: datetime = Field(..., description="When prediction should be evaluated")
    actual_value: Optional[float] = Field(None, description="Actual outcome value")
    actual_direction: Optional[str] = Field(None, description="Actual direction outcome", pattern="^(UP|DOWN|NEUTRAL)$")
    prediction_accurate: Optional[bool] = Field(None, description="Whether prediction was accurate")
    accuracy_score: Optional[float] = Field(None, description="Accuracy score (0.0 to 1.0)", ge=0.0, le=1.0)
    model_version: str = Field(default="v2.5", description="Model version used")
    market_context: Dict[str, Any] = Field(default_factory=dict, description="Market context at prediction time")
    created_at: datetime = Field(default_factory=datetime.now, description="Record creation timestamp")
    updated_at: datetime = Field(default_factory=datetime.now, description="Record update timestamp")

class AIPredictionPerformanceV2_5(BaseModel):
    """Pydantic model for AI prediction performance metrics."""
    symbol: str = Field(..., description="Trading symbol")
    time_period_days: int = Field(..., description="Time period for performance calculation", ge=1)
    total_predictions: int = Field(..., description="Total number of predictions", ge=0)
    correct_predictions: int = Field(..., description="Number of correct predictions", ge=0)
    incorrect_predictions: int = Field(..., description="Number of incorrect predictions", ge=0)
    pending_predictions: int = Field(..., description="Number of pending predictions", ge=0)
    success_rate: float = Field(..., description="Success rate (0.0 to 1.0)", ge=0.0, le=1.0)
    avg_confidence: float = Field(..., description="Average confidence score", ge=0.0, le=1.0)
    avg_accuracy_score: float = Field(..., description="Average accuracy score", ge=0.0, le=1.0)
    learning_score: float = Field(..., description="Learning effectiveness score", ge=0.0, le=1.0)
    performance_trend: str = Field(..., description="Performance trend", pattern="^(IMPROVING|STABLE|DECLINING|UNKNOWN)$")
    last_updated: datetime = Field(default_factory=datetime.now, description="Last update timestamp")

class AIPredictionRequestV2_5(BaseModel):
    """Pydantic model for AI prediction creation requests."""
    symbol: str = Field(..., description="Trading symbol")
    prediction_type: str = Field(..., description="Type of prediction")
    prediction_value: Optional[float] = Field(None, description="Predicted value")
    prediction_direction: str = Field(..., description="Predicted direction", pattern="^(UP|DOWN|NEUTRAL)$")
    confidence_score: float = Field(..., description="Confidence level", ge=0.0, le=1.0)
    time_horizon: str = Field(..., description="Time frame")
    target_timestamp: datetime = Field(..., description="Target evaluation time")
    market_context: Dict[str, Any] = Field(default_factory=dict, description="Market context")

# ===== AI ADAPTATIONS MODELS =====

class AIAdaptationV2_5(BaseModel):
    """Pydantic model for AI adaptations with full validation."""
    id: Optional[int] = Field(None, description="Database ID (auto-generated)")
    adaptation_type: str = Field(..., description="Type of adaptation ('signal_enhancement', 'threshold_adjustment', 'model_calibration')")
    adaptation_name: str = Field(..., description="Human-readable name for the adaptation")
    adaptation_description: Optional[str] = Field(None, description="Detailed description of the adaptation")
    confidence_score: float = Field(default=0.0, description="Confidence in adaptation effectiveness", ge=0.0, le=1.0)
    success_rate: float = Field(default=0.0, description="Historical success rate of adaptation", ge=0.0, le=1.0)
    adaptation_score: float = Field(default=0.0, description="Overall adaptation performance score", ge=0.0, le=1.0)
    implementation_status: str = Field(default="PENDING", description="Implementation status", pattern="^(PENDING|ACTIVE|INACTIVE|DEPRECATED)$")
    market_context: Dict[str, Any] = Field(default_factory=dict, description="Market context when adaptation was created")
    performance_metrics: Dict[str, Any] = Field(default_factory=dict, description="Performance tracking metrics")
    created_at: datetime = Field(default_factory=datetime.now, description="Record creation timestamp")
    updated_at: datetime = Field(default_factory=datetime.now, description="Record update timestamp")

    class Config:
        extra = 'allow'

class AIAdaptationPerformanceV2_5(BaseModel):
    """Pydantic model for AI adaptation performance tracking."""
    adaptation_id: int = Field(..., description="Reference to adaptation ID")
    symbol: str = Field(..., description="Trading symbol")
    time_period_days: int = Field(..., description="Time period for performance calculation", ge=1)
    total_applications: int = Field(..., description="Total times adaptation was applied", ge=0)
    successful_applications: int = Field(..., description="Number of successful applications", ge=0)
    success_rate: float = Field(..., description="Success rate percentage", ge=0.0, le=1.0)
    avg_improvement: float = Field(..., description="Average improvement in target metric", ge=0.0)
    adaptation_score: float = Field(..., description="Overall adaptation score", ge=0.0, le=1.0)
    performance_trend: str = Field(..., description="Performance trend", pattern="^(IMPROVING|STABLE|DECLINING|UNKNOWN)$")
    last_updated: datetime = Field(default_factory=datetime.now, description="Last update timestamp")

    class Config:
        extra = 'forbid'

class AIAdaptationRequestV2_5(BaseModel):
    """Pydantic model for AI adaptation creation requests."""
    adaptation_type: str = Field(..., description="Type of adaptation")
    adaptation_name: str = Field(..., description="Name for the adaptation")
    adaptation_description: Optional[str] = Field(None, description="Description of the adaptation")
    confidence_score: float = Field(default=0.5, description="Initial confidence score", ge=0.0, le=1.0)
    market_context: Dict[str, Any] = Field(default_factory=dict, description="Current market context")

    class Config:
        extra = 'forbid'

class FinalAnalysisBundleV2_5(BaseModel):
    processed_data_bundle: ProcessedDataBundleV2_5
    scored_signals_v2_5: Dict[str, List[SignalPayloadV2_5]] = Field(default_factory=dict)
    key_levels_data_v2_5: KeyLevelsDataV2_5
    active_recommendations_v2_5: List[ActiveRecommendationPayloadV2_5] = Field(default_factory=list)
    bundle_timestamp: datetime
    target_symbol: str
    system_status_messages: List[str] = Field(default_factory=list)
    atif_recommendations_v2_5: Optional[List[ATIFStrategyDirectivePayloadV2_5]] = None
    news_intelligence_v2_5: Optional[Dict[str, Any]] = Field(None, description="Diabolical news intelligence analysis")
    ai_predictions_v2_5: Optional[List[AIPredictionV2_5]] = Field(None, description="AI predictions for this analysis")
    """
    The main analysis bundle for the dashboard. Now includes ATIF recommendations, diabolical news intelligence, and AI predictions for apex predator analysis.
    """
    class Config:
        arbitrary_types_allowed = True

class TimeOfDayDefinitions(BaseModel):
    market_open: str = Field("09:30:00", description="Market open time in HH:MM:SS format")
    market_close: str = Field("16:00:00", description="Market close time in HH:MM:SS format")
    pre_market_start: str = Field("04:00:00", description="Pre-market start time in HH:MM:SS format")
    after_hours_end: str = Field("20:00:00", description="After hours end time in HH:MM:SS format")
    eod_pressure_calc_time: str = Field("15:00:00", description="Time for end-of-day pressure calculations in HH:MM:SS format")

    class Config:
        extra = 'forbid'

# Dashboard Mode Configuration Models
class DashboardModeSettings(BaseModel):
    """Defines a single dashboard mode configuration"""
    label: str = Field(..., description="Display label for the mode")
    module_name: str = Field(..., description="Python module name to import for this mode")
    charts: List[str] = Field(default_factory=list, description="List of chart/component names to display in this mode")
    
    class Config:
        extra = 'forbid'

class MainDashboardDisplaySettings(BaseModel):
    """Settings specific to main dashboard display components"""
    regime_indicator: Dict[str, Any] = Field(default_factory=lambda: {
        "title": "Market Regime",
        "regime_colors": {
            "default": "secondary",
            "bullish": "success", 
            "bearish": "danger",
            "neutral": "info",
            "unclear": "warning"
        }
    })
    flow_gauge: Dict[str, Any] = Field(default_factory=lambda: {
        "height": 200,
        "indicator_font_size": 16,
        "number_font_size": 24,
        "axis_range": [-3, 3],
        "threshold_line_color": "white",
        "margin": {"t": 60, "b": 40, "l": 20, "r": 20},
        "steps": [
            {"range": [-3, -2], "color": "#d62728"},
            {"range": [-2, -0.5], "color": "#ff9896"},
            {"range": [-0.5, 0.5], "color": "#aec7e8"},
            {"range": [0.5, 2], "color": "#98df8a"},
            {"range": [2, 3], "color": "#2ca02c"}
        ]
    })
    gib_gauge: Dict[str, Any] = Field(default_factory=lambda: {
        "height": 180,
        "indicator_font_size": 14,
        "number_font_size": 20,
        "axis_range": [-1, 1],
        "dollar_axis_range": [-1000000, 1000000],
        "threshold_line_color": "white",
        "margin": {"t": 50, "b": 30, "l": 15, "r": 15},
        "steps": [
            {"range": [-1, -0.5], "color": "#d62728"},
            {"range": [-0.5, -0.1], "color": "#ff9896"},
            {"range": [-0.1, 0.1], "color": "#aec7e8"},
            {"range": [0.1, 0.5], "color": "#98df8a"},
            {"range": [0.5, 1], "color": "#2ca02c"}
        ],
        "dollar_steps": [
            {"range": [-1000000, -500000], "color": "#d62728"},
            {"range": [-500000, -100000], "color": "#ff9896"},
            {"range": [-100000, 100000], "color": "#aec7e8"},
            {"range": [100000, 500000], "color": "#98df8a"},
            {"range": [500000, 1000000], "color": "#2ca02c"}
        ]
    })
    mini_heatmap: Dict[str, Any] = Field(default_factory=lambda: {
        "height": 150,
        "colorscale": "RdYlGn",
        "margin": {"t": 50, "b": 30, "l": 40, "r": 40}
    })
    recommendations_table: Dict[str, Any] = Field(default_factory=lambda: {
        "title": "ATIF Recommendations",
        "max_rationale_length": 50,
        "page_size": 5,
        "style_cell": {"textAlign": "left", "padding": "5px", "minWidth": "80px", "width": "auto", "maxWidth": "200px"},
        "style_header": {"backgroundColor": "rgb(30, 30, 30)", "fontWeight": "bold", "color": "white"},
        "style_data": {"backgroundColor": "rgb(50, 50, 50)", "color": "white"}
    })
    ticker_context: Dict[str, Any] = Field(default_factory=lambda: {
        "title": "Ticker Context"
    })
    
    class Config:
        extra = 'forbid'

class DashboardModeCollection(BaseModel):
    """Collection of all dashboard modes"""
    main: DashboardModeSettings = Field(default_factory=lambda: DashboardModeSettings(
        label="Main Dashboard",
        module_name="main_dashboard_display_v2_5",
        charts=["regime_display", "flow_gauges", "gib_gauges", "recommendations_table"]
    ))
    flow: DashboardModeSettings = Field(default_factory=lambda: DashboardModeSettings(
        label="Flow Analysis", 
        module_name="flow_mode_display_v2_5",
        charts=["net_value_heatmap_viz", "net_cust_delta_flow_viz", "net_cust_gamma_flow_viz", "net_cust_vega_flow_viz"]
    ))
    structure: DashboardModeSettings = Field(default_factory=lambda: DashboardModeSettings(
        label="Structure & Positioning",
        module_name="structure_mode_display_v2_5", 
        charts=["mspi_components", "sai_ssi_displays"]
    ))
    timedecay: DashboardModeSettings = Field(default_factory=lambda: DashboardModeSettings(
        label="Time Decay & Pinning",
        module_name="time_decay_mode_display_v2_5",
        charts=["tdpi_displays", "vci_strike_charts"]
    ))
    advanced: DashboardModeSettings = Field(default_factory=lambda: DashboardModeSettings(
        label="Advanced Flow Metrics",
        module_name="advanced_flow_mode_display_v2_5",
        charts=["vapi_gauges", "dwfd_gauges", "tw_laf_gauges"]
    ))
    
    class Config:
        extra = 'forbid'

class DagAlphaCoeffs(BaseModel):
    aligned: float = Field(default=1.35, description="Coefficient for aligned market conditions")
    opposed: float = Field(default=0.65, description="Coefficient for opposed market conditions")
    neutral: float = Field(default=1.0, description="Coefficient for neutral market conditions")

class TdpiBetaCoeffs(BaseModel):
    aligned: float = Field(default=1.35, description="Coefficient for aligned market conditions")
    opposed: float = Field(default=0.65, description="Coefficient for opposed market conditions")
    neutral: float = Field(default=1.0, description="Coefficient for neutral market conditions")

class VriGammaCoeffs(BaseModel):
    aligned: float = Field(default=1.35, description="Coefficient for aligned market conditions")
    opposed: float = Field(default=0.65, description="Coefficient for opposed market conditions")
    neutral: float = Field(default=1.0, description="Coefficient for neutral market conditions")

class CoefficientsSettings(BaseModel):
    dag_alpha: DagAlphaCoeffs = Field(default=DagAlphaCoeffs())
    tdpi_beta: TdpiBetaCoeffs = Field(default=TdpiBetaCoeffs())
    vri_gamma: VriGammaCoeffs = Field(default=VriGammaCoeffs())

    class Config:
        extra = 'forbid'

class RegimeRule(BaseModel):
    """Individual rule for market regime evaluation."""
    metric: str = Field(..., description="Metric name to evaluate")
    operator: str = Field(..., description="Comparison operator (_gt, _lt, _eq, etc.)")
    value: Union[str, float, int, bool] = Field(..., description="Target value for comparison")
    selector: Optional[str] = Field(None, description="Optional selector like @ATM")
    aggregator: Optional[str] = Field(None, description="Optional aggregation method")

    class Config:
        extra = 'forbid'

class MarketRegimeEngineSettings(BaseModel):
    default_regime: str = Field(default="REGIME_UNCLEAR_OR_TRANSITIONING", description="Default market regime")
    regime_evaluation_order: List[str] = Field(
        default=[
            "REGIME_SPX_0DTE_FRIDAY_EOD_VANNA_CASCADE_POTENTIAL_BULLISH",
            "REGIME_SPY_PRE_FOMC_VOL_COMPRESSION_WITH_DWFD_ACCUMULATION",
            "REGIME_HIGH_VAPI_FA_BULLISH_MOMENTUM_UNIVERSAL",
            "REGIME_ADAPTIVE_STRUCTURE_BREAKDOWN_WITH_DWFD_CONFIRMATION_BEARISH_UNIVERSAL",
            "REGIME_VOL_EXPANSION_IMMINENT_VRI0DTE_BULLISH",
            "REGIME_VOL_EXPANSION_IMMINENT_VRI0DTE_BEARISH",
            "REGIME_NVP_STRONG_BUY_IMBALANCE_AT_KEY_STRIKE",
            "REGIME_EOD_HEDGING_PRESSURE_BUY",
            "REGIME_EOD_HEDGING_PRESSURE_SELL",
            "REGIME_SIDEWAYS_MARKET",
            "REGIME_HIGH_VOLATILITY"
        ],
        description="Order in which to evaluate market regimes"
    )
    regime_rules: Dict[str, List[RegimeRule]] = Field(
        default_factory=dict,
        description="Rules for different market regimes - each regime has a list of rules (AND logic)"
    )

    class Config:
        extra = 'forbid'

class SystemSettings(BaseModel):
    project_root_override: Optional[str] = Field(None, description="Absolute path to override the auto-detected project root. Use null for auto-detection.")
    logging_level: str = Field("INFO", description="The minimum level of logs to record.")
    log_to_file: bool = Field(True, description="If true, logs will be written to the file specified in log_file_path.")
    log_file_path: str = Field("logs/eots_v2_5.log", description="Relative path from project root for the log file.", pattern="\\.log$")
    max_log_file_size_bytes: int = Field(10485760, description="Maximum size of a single log file in bytes before rotation.", ge=1024)
    backup_log_count: int = Field(5, description="Number of old log files to keep after rotation.", ge=0)
    live_mode: bool = Field(True, description="If true, system will only use live data sources and fail fast on errors.")
    fail_fast_on_errors: bool = Field(True, description="If true, system will halt on any data quality or API errors.")
    enable_ai_intelligence: bool = Field(True, description="Enable AI intelligence features including news analysis and unified AI orchestration.")
    enable_unified_orchestrator: bool = Field(True, description="Enable unified AI orchestrator with MCP servers and enhanced intelligence.")
    enable_multi_database: bool = Field(True, description="Enable multi-database support for enhanced data storage.")
    metrics_for_dynamic_threshold_distribution_tracking: List[str] = Field(
        default=["GIB_OI_based_Und", "VAPI_FA_Z_Score_Und", "DWFD_Z_Score_Und", "TW_LAF_Z_Score_Und"],
        description="List of underlying aggregate metric names to track historically for dynamic threshold calculations."
    )
    signal_activation: Dict[str, Any] = Field(default_factory=lambda: {"EnableAllSignals": True}, description="Toggles for enabling or disabling specific signal generation routines.")

class ConvexValueAuthSettings(BaseModel):
    use_env_variables: bool = Field(True, description="Whether to use environment variables for authentication.")
    auth_method: str = Field("email_password", description="Authentication method for ConvexValue API.")

class DataFetcherSettings(BaseModel):
    convexvalue_auth: ConvexValueAuthSettings = Field(default_factory=lambda: ConvexValueAuthSettings(
        use_env_variables=True,
        auth_method="email_password"
    ), description="Authentication settings for ConvexValue.")
    tradier_api_key: str = Field(..., description="API Key for Tradier.")
    tradier_account_id: str = Field(..., description="Account ID for Tradier.")
    max_retries: int = Field(3, description="Maximum number of retry attempts for a failing API call.", ge=0)
    retry_delay_seconds: float = Field(5, description="Base delay in seconds between API call retries.", ge=0)

class DataManagementSettings(BaseModel):
    data_cache_dir: str = Field("data_cache_v2_5", description="Directory for caching data.")
    historical_data_store_dir: str = Field("data_cache_v2_5/historical_data_store", description="Directory for historical data storage.")
    performance_data_store_dir: str = Field("data_cache_v2_5/performance_data_store", description="Directory for performance data storage.")

class EnhancedFlowMetricSettings(BaseModel):
    vapi_fa_params: Dict[str, Any] = Field(default_factory=dict)
    acceleration_calculation_intervals: List[str] = Field(default_factory=list)
    dwfd_params: Dict[str, Any] = Field(default_factory=dict)
    tw_laf_params: Dict[str, Any] = Field(default_factory=dict)
    # Isolated configuration parameters
    z_score_window: int = Field(20, description="Window size for Z-score calculations in enhanced flow metrics", ge=5, le=200)
    time_intervals: List[int] = Field(default_factory=lambda: [5, 15, 30], description="Time intervals for TW-LAF calculations")
    liquidity_weight: float = Field(0.3, description="Liquidity weight for TW-LAF calculations", ge=0.0, le=1.0)
    divergence_threshold: float = Field(1.5, description="Threshold for DWFD divergence detection", ge=0.1, le=10.0)
    lookback_periods: List[int] = Field(default_factory=lambda: [5, 10, 20], description="Lookback periods for VAPI-FA calculations")

    class Config:
        extra = 'forbid'

class StrategySettings(BaseModel):
    class Config:
        extra = 'allow'

class LearningParams(BaseModel):
    performance_tracker_query_lookback: int = Field(90, description="Number of days of historical performance data to consider for learning.", ge=1)
    learning_rate_for_signal_weights: float = Field(0.05, description="How aggressively ATIF adjusts signal weights based on new performance data (0-1 scale).", ge=0, le=1)
    learning_rate_for_target_adjustments: float = Field(0.02, description="How aggressively ATIF adjusts target parameters based on performance data (0-1 scale).", ge=0, le=1)
    min_trades_for_statistical_significance: int = Field(20, description="Minimum number of trades required for statistical significance in learning adjustments.", ge=1)

    class Config:
        extra = 'forbid'

class AdaptiveTradeIdeaFrameworkSettings(BaseModel):
    min_conviction_to_initiate_trade: float = Field(2.5, description="The minimum ATIF conviction score (0-5 scale) required to generate a new trade recommendation.", ge=0, le=5)
    signal_integration_params: Dict[str, Any] = Field(default_factory=dict)
    regime_context_weight_multipliers: Dict[str, float] = Field(default_factory=dict)
    conviction_mapping_params: Dict[str, Any] = Field(default_factory=dict)
    strategy_specificity_rules: List[Dict[str, Any]] = Field(default_factory=list)
    intelligent_recommendation_management_rules: Dict[str, Any] = Field(default_factory=dict)
    learning_params: LearningParams = Field(default_factory=lambda: LearningParams(
        performance_tracker_query_lookback=90,
        learning_rate_for_signal_weights=0.05,
        learning_rate_for_target_adjustments=0.02,
        min_trades_for_statistical_significance=20
    ))

    class Config:
        extra = 'forbid'

class TickerContextAnalyzerSettings(BaseModel):
    lookback_days: int = 252  # 1 year
    correlation_window: int = 60
    volatility_windows: List[int] = Field(default_factory=lambda: [1, 5, 20])
    volume_threshold: int = 1000000
    use_yahoo_finance: bool = False
    yahoo_finance_rate_limit_seconds: float = 2.0
    SPY: Dict[str, Any] = Field(default_factory=dict)
    DEFAULT_TICKER_PROFILE: Dict[str, Any] = Field(default_factory=dict)

    class Config:
        extra = 'forbid'

class KeyLevelIdentifierSettings(BaseModel):
    lookback_periods: int = 20
    min_touches: int = 2
    level_tolerance: float = 0.005  # 0.5%
    volume_threshold: float = 1.5
    oi_threshold: int = 1000
    gamma_threshold: float = 0.1
    nvp_support_quantile: float = 0.95
    nvp_resistance_quantile: float = 0.95

    class Config:
        extra = 'forbid'

class HeatmapGenerationSettings(BaseModel):
    ugch_params: Dict[str, Any] = Field(default_factory=dict)
    sgdhp_params: Dict[str, Any] = Field(default_factory=dict)
    # Isolated configuration parameters
    flow_normalization_window: int = Field(100, description="Window size for flow normalization in enhanced heatmap calculations", ge=10, le=500)

    class Config:
        extra = 'forbid'

class PerformanceTrackerSettingsV2_5(BaseModel):
    performance_data_directory: str = Field(..., description="Directory for storing performance tracking data.")
    historical_window_days: int = Field(365, description="Number of days to consider for historical performance.", ge=1)
    weight_smoothing_factor: float = Field(0.1, description="Smoothing factor for performance weights (0-1 scale).", ge=0, le=1)
    min_sample_size: int = Field(10, description="Minimum number of samples required for reliable performance metrics.", ge=1)
    confidence_threshold: float = Field(0.75, description="Minimum confidence level required for performance-based adjustments.", ge=0, le=1)
    update_interval_seconds: int = Field(3600, description="Interval in seconds between performance data updates.", ge=1)
    tracking_enabled: bool = Field(True, description="Whether performance tracking is enabled.")
    metrics_to_track: List[str] = Field(default_factory=lambda: ["returns", "sharpe_ratio", "max_drawdown", "win_rate"], description="List of metrics to track.")
    reporting_frequency: str = Field("daily", description="Frequency of performance reporting.")
    benchmark_symbol: str = Field("SPY", description="Symbol to use as a benchmark for performance comparison.")

    class Config:
        extra = 'forbid'

class AdaptiveMetricParameters(BaseModel):
    a_mspi_settings: Dict[str, Any] = Field(default_factory=dict)
    a_dag_settings: Dict[str, Any] = Field(default_factory=dict)
    e_sdag_settings: Dict[str, Any] = Field(default_factory=dict)
    d_tdpi_settings: Dict[str, Any] = Field(default_factory=dict)
    vri_2_0_settings: Dict[str, Any] = Field(default_factory=dict)
    # Isolated configuration parameters for enhanced heatmap
    enhanced_heatmap_settings: Dict[str, Any] = Field(default_factory=dict)

    class Config:
        extra = 'forbid'

class DataProcessorSettings(BaseModel):
    factors: Dict[str, Any] = Field(default_factory=dict)
    coefficients: CoefficientsSettings = Field(default_factory=CoefficientsSettings)
    iv_context_parameters: Dict[str, Any] = Field(default_factory=dict)
    vci_0dte_parameters: Dict[str, Any] = Field(default_factory=lambda: {
        "high_concentration_threshold": 0.6,
        "extreme_concentration_threshold": 0.8,
        "min_vanna_oi_threshold": 1000.0,
        "top_strikes_percentile": 0.8
    })

    class Config:
        extra = 'forbid'

class VisualizationSettings(BaseModel):
    dashboard_refresh_interval_seconds: int = Field(60, description="Interval in seconds between dashboard refreshes")
    max_table_rows_signals_insights: int = Field(10, description="Maximum number of rows to display in signals and insights tables")
    dashboard: Dict[str, Any] = Field(default_factory=lambda: {
        "host": "localhost",
        "port": 8050,
        "debug": False,
        "auto_refresh_seconds": 30,
        "timestamp_format": "%Y-%m-%d %H:%M:%S %Z",
        "defaults": {
            "symbol": "SPY",
            "refresh_interval_seconds": 30
        },
        "modes_detail_config": {
            "main": {
                "label": "Main Dashboard",
                "module_name": "main_dashboard_display_v2_5",
                "charts": ["regime_display", "flow_gauges", "gib_gauges", "recommendations_table"]
            },
            "flow": {
                "label": "Flow Analysis",
                "module_name": "flow_mode_display_v2_5",
                "charts": ["net_value_heatmap_viz", "net_cust_delta_flow_viz", "net_cust_gamma_flow_viz", "net_cust_vega_flow_viz"]
            },
            "structure": {
                "label": "Structure & Positioning",
                "module_name": "structure_mode_display_v2_5",
                "charts": ["mspi_components", "sai_ssi_displays"]
            },
            "timedecay": {
                "label": "Time Decay & Pinning",
                "module_name": "time_decay_mode_display_v2_5",
                "charts": ["tdpi_displays", "vci_strike_charts"]
            },
            "advanced": {
                "label": "Advanced Flow Metrics",
                "module_name": "advanced_flow_mode_display_v2_5",
                "charts": ["vapi_gauges", "dwfd_gauges", "tw_laf_gauges"]
            }
        },
        "main_dashboard_settings": {
            "regime_indicator": {
                "title": "Market Regime",
                "regime_colors": {
                    "default": "secondary",
                    "bullish": "success",
                    "bearish": "danger",
                    "neutral": "info",
                    "unclear": "warning"
                }
            },
            "flow_gauge": {
                "height": 200,
                "indicator_font_size": 16,
                "number_font_size": 24,
                "axis_range": [-3, 3],
                "threshold_line_color": "white",
                "margin": {"t": 60, "b": 40, "l": 20, "r": 20},
                "steps": [
                    {"range": [-3, -2], "color": "#d62728"},
                    {"range": [-2, -0.5], "color": "#ff9896"},
                    {"range": [-0.5, 0.5], "color": "#aec7e8"},
                    {"range": [0.5, 2], "color": "#98df8a"},
                    {"range": [2, 3], "color": "#2ca02c"}
                ]
            },
            "gib_gauge": {
                "height": 180,
                "indicator_font_size": 14,
                "number_font_size": 20,
                "axis_range": [-1, 1],
                "dollar_axis_range": [-1000000, 1000000],
                "threshold_line_color": "white",
                "margin": {"t": 50, "b": 30, "l": 15, "r": 15},
                "steps": [
                    {"range": [-1, -0.5], "color": "#d62728"},
                    {"range": [-0.5, -0.1], "color": "#ff9896"},
                    {"range": [-0.1, 0.1], "color": "#aec7e8"},
                    {"range": [0.1, 0.5], "color": "#98df8a"},
                    {"range": [0.5, 1], "color": "#2ca02c"}
                ],
                "dollar_steps": [
                    {"range": [-1000000, -500000], "color": "#d62728"},
                    {"range": [-500000, -100000], "color": "#ff9896"},
                    {"range": [-100000, 100000], "color": "#aec7e8"},
                    {"range": [100000, 500000], "color": "#98df8a"},
                    {"range": [500000, 1000000], "color": "#2ca02c"}
                ]
            },
            "mini_heatmap": {
                "height": 150,
                "colorscale": "RdYlGn",
                "margin": {"t": 50, "b": 30, "l": 40, "r": 40}
            },
            "recommendations_table": {
                "title": "ATIF Recommendations",
                "max_rationale_length": 50,
                "page_size": 5,
                "style_cell": {"textAlign": "left", "padding": "5px", "minWidth": "80px", "width": "auto", "maxWidth": "200px"},
                "style_header": {"backgroundColor": "rgb(30, 30, 30)", "fontWeight": "bold", "color": "white"},
                "style_data": {"backgroundColor": "rgb(50, 50, 50)", "color": "white"}
            },
            "ticker_context": {
                "title": "Ticker Context"
            }
        }
    })

    class Config:
        extra = 'forbid'

class SymbolDefaultOverridesStrategySettingsTargets(BaseModel):
    target_atr_stop_loss_multiplier: float = 1.5

    class Config:
        extra = 'forbid'

class SymbolDefaultOverridesStrategySettings(BaseModel):
    targets: SymbolDefaultOverridesStrategySettingsTargets = Field(default_factory=SymbolDefaultOverridesStrategySettingsTargets)

    class Config:
        extra = 'forbid'

class SymbolDefaultOverrides(BaseModel):
    strategy_settings: Optional[SymbolDefaultOverridesStrategySettings] = Field(default_factory=SymbolDefaultOverridesStrategySettings)

    class Config:
        extra = 'forbid'

class SymbolSpecificOverrides(BaseModel):
    DEFAULT: Optional[SymbolDefaultOverrides] = Field(default_factory=SymbolDefaultOverrides)
    SPY: Optional[Dict[str, Any]] = Field(default_factory=dict)
    AAPL: Optional[Dict[str, Any]] = Field(default_factory=dict)

    class Config:
        extra = 'forbid'

class DatabaseSettings(BaseModel):
    host: str = Field(..., description="Database host address")
    port: int = Field(5432, description="Database port number")
    database: str = Field(..., description="Database name")
    user: str = Field(..., description="Database username")
    password: str = Field(..., description="Database password")
    min_connections: int = Field(1, description="Minimum number of database connections")
    max_connections: int = Field(10, description="Maximum number of database connections")

    class Config:
        extra = 'forbid'

# ===== AI PERFORMANCE TRACKER MODELS =====

class AIPerformanceDataV2_5(BaseModel):
    """Comprehensive Pydantic model for AI Performance Tracker dashboard data."""
    # Time series data
    dates: List[str] = Field(default_factory=list, description="List of dates for performance tracking")
    accuracy: List[float] = Field(default_factory=list, description="Daily accuracy percentages")
    confidence: List[float] = Field(default_factory=list, description="Daily confidence scores")
    learning_curve: List[float] = Field(default_factory=list, description="Cumulative learning improvement")

    # Summary statistics
    total_predictions: int = Field(0, description="Total number of predictions made", ge=0)
    successful_predictions: int = Field(0, description="Number of successful predictions", ge=0)
    success_rate: float = Field(0.0, description="Overall success rate", ge=0.0, le=1.0)
    avg_confidence: float = Field(0.0, description="Average confidence score", ge=0.0, le=1.0)
    improvement_rate: float = Field(0.0, description="Rate of improvement over time")
    learning_score: float = Field(0.0, description="Overall learning effectiveness score", ge=0.0, le=10.0)

    # Data source and metadata
    data_source: str = Field("Unknown", description="Source of performance data")
    last_updated: datetime = Field(default_factory=datetime.now, description="Last update timestamp")
    symbol: str = Field("SPY", description="Primary symbol for performance tracking")

    # Enhanced intelligence features
    diabolical_intelligence_active: bool = Field(False, description="Whether diabolical intelligence is active")
    sentiment_regime: str = Field("NEUTRAL", description="Current sentiment regime")
    intelligence_confidence: str = Field("50.0%", description="Intelligence confidence level")
    diabolical_insight: str = Field("😈 Apex predator analyzing...", description="Diabolical AI insight")

    class Config:
        extra = 'allow'

class AILearningStatsV2_5(BaseModel):
    """Pydantic model for AI learning statistics in 6-metric comprehensive format."""
    patterns_learned: int = Field(247, description="Number of patterns learned", ge=0)
    success_rate: float = Field(0.73, description="Learning success rate", ge=0.0, le=1.0)
    adaptation_score: float = Field(8.4, description="Adaptation effectiveness score", ge=0.0, le=10.0)
    memory_nodes: int = Field(1432, description="Number of active memory nodes", ge=0)
    active_connections: int = Field(3847, description="Number of active neural connections", ge=0)
    learning_velocity: float = Field(0.85, description="Rate of learning velocity", ge=0.0, le=1.0)

    # Metadata
    data_source: str = Field("Database", description="Source of learning statistics")
    last_updated: datetime = Field(default_factory=datetime.now, description="Last update timestamp")

    class Config:
        extra = 'allow'

class AIMCPStatusV2_5(BaseModel):
    """Pydantic model for MCP (Model Context Protocol) server status."""
    memory_server_active: bool = Field(False, description="Memory MCP server status")
    sequential_thinking_active: bool = Field(False, description="Sequential thinking MCP server status")
    exa_search_active: bool = Field(False, description="Exa search MCP server status")
    context7_active: bool = Field(False, description="Context7 MCP server status")

    # Connection details
    total_servers: int = Field(4, description="Total number of MCP servers", ge=0)
    active_servers: int = Field(0, description="Number of active MCP servers", ge=0)
    connection_health: float = Field(0.0, description="Overall connection health", ge=0.0, le=1.0)

    # Status messages
    status_message: str = Field("MCP servers initializing...", description="Current status message")
    last_check: datetime = Field(default_factory=datetime.now, description="Last status check timestamp")

    class Config:
        extra = 'allow'

class AISystemHealthV2_5(BaseModel):
    """Comprehensive Pydantic model for AI system health monitoring."""
    # Database connectivity
    database_connected: bool = Field(False, description="Database connection status")
    ai_tables_available: bool = Field(False, description="AI tables availability status")

    # Component health
    predictions_manager_healthy: bool = Field(False, description="AI Predictions Manager health")
    learning_system_healthy: bool = Field(False, description="AI Learning System health")
    adaptation_engine_healthy: bool = Field(False, description="AI Adaptation Engine health")

    # Performance metrics
    overall_health_score: float = Field(0.0, description="Overall system health score", ge=0.0, le=1.0)
    response_time_ms: float = Field(0.0, description="Average response time in milliseconds", ge=0.0)
    error_rate: float = Field(0.0, description="System error rate", ge=0.0, le=1.0)

    # Status details
    status_message: str = Field("System initializing...", description="Current system status message")
    last_health_check: datetime = Field(default_factory=datetime.now, description="Last health check timestamp")

    # Detailed component status
    component_status: Dict[str, Any] = Field(default_factory=dict, description="Detailed component status information")

    class Config:
        extra = 'allow'

# ===== AI PREDICTIONS SETTINGS =====

class AIPerformanceTrackerSettings(BaseModel):
    """Configuration settings for AI Performance Tracker dashboard component."""
    title: str = Field("📊 AI Performance Tracker", description="Display title for performance tracker")
    height: int = Field(250, description="Chart height in pixels", ge=100)
    lookback_days: int = Field(30, description="Number of days to look back for performance data", ge=1)
    show_learning_curve: bool = Field(True, description="Whether to show learning curve visualization")
    refresh_interval: int = Field(30, description="Refresh interval in seconds", ge=5)
    max_data_points: int = Field(100, description="Maximum data points to display", ge=10)

    # Performance thresholds
    excellent_threshold: float = Field(0.85, description="Threshold for excellent performance", ge=0.0, le=1.0)
    good_threshold: float = Field(0.70, description="Threshold for good performance", ge=0.0, le=1.0)
    poor_threshold: float = Field(0.50, description="Threshold for poor performance", ge=0.0, le=1.0)

    # Display options
    show_confidence_bands: bool = Field(True, description="Show confidence bands on charts")
    show_trend_analysis: bool = Field(True, description="Show trend analysis")
    enable_real_time_updates: bool = Field(True, description="Enable real-time performance updates")

    class Config:
        extra = 'allow'

class AIPredictionsSettings(BaseModel):
    """Configuration settings for AI predictions system."""
    enabled: bool = Field(True, description="Enable AI predictions generation")
    auto_create_predictions: bool = Field(True, description="Automatically create predictions during analysis")
    default_time_horizon: str = Field("4H", description="Default prediction time horizon")
    min_confidence_threshold: float = Field(0.5, description="Minimum confidence to create prediction", ge=0.0, le=1.0)
    max_predictions_per_symbol: int = Field(10, description="Maximum active predictions per symbol", ge=1)
    evaluation_frequency_minutes: int = Field(60, description="How often to evaluate predictions (minutes)", ge=1)
    auto_evaluation_enabled: bool = Field(True, description="Automatically evaluate predictions when target time reached")
    performance_tracking_days: int = Field(30, description="Days to track for performance metrics", ge=1)
    prediction_types: List[str] = Field(default_factory=lambda: [
        "eots_direction", "price_target", "volatility_forecast", "regime_transition"
    ], description="Supported prediction types")
    confidence_calibration: Dict[str, float] = Field(default_factory=lambda: {
        "strong_signal_threshold": 3.0,
        "moderate_signal_threshold": 1.5,
        "max_confidence": 0.85,
        "min_confidence": 0.5
    }, description="Confidence score calibration parameters")

    class Config:
        extra = 'forbid'

class IntradayCollectorSettings(BaseModel):
    watched_tickers: List[str] = Field(default_factory=lambda: [
        "SPY", "SPX", "QQQ", "IWM", "VIX", "TSLA", "AMZN", "AAPL", "META", "MSFT", "NVDA"
    ], description="List of tickers to collect intraday metrics for.")
    metrics: List[str] = Field(default_factory=lambda: [
        "vapi_fa", "dwfd", "tw_laf",
        "vapifa_zscore_history", "dwfd_zscore_history", "twlaf_zscore_history",
        "rolling_flows", "nvp_by_strike", "nvp_vol_by_strike", "strikes",
        "greek_flows", "flow_ratios"
    ], description="Gauge and advanced analytics metrics to collect for intraday dashboard display.")
    cache_dir: str = Field(default="cache/intraday_metrics", description="Directory for intraday metric cache files.")
    collection_interval_seconds: int = Field(default=5, description="Interval in seconds between metric collections.")
    market_open_time: str = Field(default="09:30:00", description="Market open time (HH:MM:SS).")
    market_close_time: str = Field(default="16:00:00", description="Market close time (HH:MM:SS).")
    reset_at_eod: bool = Field(default=True, description="Whether to wipe cache at end of day.")
    class Config:
        extra = 'forbid'

class EOTSConfigV2_5(BaseModel):
    system_settings: SystemSettings = Field(default_factory=lambda: SystemSettings(
        project_root_override=None,
        logging_level="INFO",
        log_to_file=True,
        log_file_path="logs/eots_v2_5.log",
        max_log_file_size_bytes=10485760,
        backup_log_count=5,
        live_mode=True,
        fail_fast_on_errors=True,
        metrics_for_dynamic_threshold_distribution_tracking=[],
        signal_activation={}
    ))
    data_fetcher_settings: DataFetcherSettings
    data_management_settings: DataManagementSettings = Field(default_factory=lambda: DataManagementSettings(
        data_cache_dir="data_cache_v2_5",
        historical_data_store_dir="data_cache_v2_5/historical_data_store",
        performance_data_store_dir="data_cache_v2_5/performance_data_store"
    ))
    database_settings: Optional[DatabaseSettings] = Field(None, description="Database connection settings")
    data_processor_settings: DataProcessorSettings = Field(default_factory=lambda: DataProcessorSettings())
    strategy_settings: StrategySettings = Field(default_factory=lambda: StrategySettings())
    adaptive_metric_parameters: AdaptiveMetricParameters = Field(default_factory=lambda: AdaptiveMetricParameters())
    enhanced_flow_metric_settings: EnhancedFlowMetricSettings = Field(default_factory=lambda: EnhancedFlowMetricSettings(
        z_score_window=20,
        liquidity_weight=0.3,
        divergence_threshold=1.5
    ))
    adaptive_trade_idea_framework_settings: AdaptiveTradeIdeaFrameworkSettings
    ticker_context_analyzer_settings: TickerContextAnalyzerSettings = Field(default_factory=lambda: TickerContextAnalyzerSettings())
    key_level_identifier_settings: KeyLevelIdentifierSettings = Field(default_factory=lambda: KeyLevelIdentifierSettings())
    heatmap_generation_settings: HeatmapGenerationSettings = Field(default_factory=lambda: HeatmapGenerationSettings(
        flow_normalization_window=100
    ))
    market_regime_engine_settings: MarketRegimeEngineSettings = Field(default_factory=lambda: MarketRegimeEngineSettings())
    visualization_settings: VisualizationSettings = Field(default_factory=lambda: VisualizationSettings(
        dashboard_refresh_interval_seconds=60,
        max_table_rows_signals_insights=10,
        dashboard={}
    ))
    symbol_specific_overrides: SymbolSpecificOverrides = Field(default_factory=lambda: SymbolSpecificOverrides())
    performance_tracker_settings_v2_5: PerformanceTrackerSettingsV2_5 = Field(default_factory=lambda: PerformanceTrackerSettingsV2_5(
        performance_data_directory="data_cache_v2_5/performance_data_store",
        historical_window_days=365,
        weight_smoothing_factor=0.1,
        min_sample_size=10,
        confidence_threshold=0.75,
        update_interval_seconds=3600,
        tracking_enabled=True,
        reporting_frequency="daily",
        benchmark_symbol="SPY"
    ))
    time_of_day_definitions: TimeOfDayDefinitions = Field(default_factory=lambda: TimeOfDayDefinitions(
        market_open="09:30:00",
        market_close="16:00:00",
        pre_market_start="04:00:00",
        after_hours_end="20:00:00",
        eod_pressure_calc_time="15:00:00"
    ))
    intraday_collector_settings: IntradayCollectorSettings = Field(default_factory=lambda: IntradayCollectorSettings())
    ai_predictions_settings: AIPredictionsSettings = Field(default_factory=lambda: AIPredictionsSettings())
    ai_performance_tracker_settings: AIPerformanceTrackerSettings = Field(default_factory=lambda: AIPerformanceTrackerSettings())

    class Config:
        json_schema_extra = {
            "$schema": "http://json-schema.org/draft-07/schema#",
            "title": "EOTS_V2_5_Config_Schema",
            "description": "Canonical schema for EOTS v2.5 configuration (config_v2_5.json). Defines all valid parameters, types, defaults, and descriptions for system operation."
        }
        extra = 'forbid'

if __name__ == '__main__':
    print("Pydantic models defined.")
