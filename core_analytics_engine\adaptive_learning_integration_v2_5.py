"""
Adaptive Learning Integration for EOTS v2.5 - UNIFIED AI INTEGRATION
====================================================================

This module integrates the Unified AI Intelligence System with the existing
EOTS system, providing scheduled learning cycles and real-time adaptation.

Features:
- Pydantic-First Architecture: All models validated against eots_schemas_v2_5
- Unified AI Intelligence: Integrated with consolidated AI system
- Scheduled learning cycles (daily, weekly, monthly)
- Real-time parameter adjustment based on performance
- Learning history tracking with schema validation
- Performance validation against EOTS standards
- Rollback capabilities for failed optimizations

Author: EOTS v2.5 Development Team - "Unified AI Integration Division"
"""

import logging
import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from pathlib import Path
import schedule
import threading

# Import unified AI intelligence system
from core_analytics_engine.unified_ai_intelligence_system_v2_5 import (
    UnifiedAIIntelligenceSystemV2_5,
    UnifiedLearningResult,
    SystemDependencies,
    get_unified_ai_intelligence_system,
    run_unified_learning_for_symbol
)
from data_management.historical_data_storage_v2_5 import HistoricalDataStorageV2_5
from data_models.eots_schemas_v2_5 import FinalAnalysisBundleV2_5

logger = logging.getLogger(__name__)

class AdaptiveLearningIntegrationV2_5:
    """
    Integrates Unified AI Intelligence System with the EOTS system.
    Manages learning schedules, parameter updates, and performance tracking.

    Features:
    - Pydantic-First Architecture with schema validation
    - Unified AI Intelligence System integration
    - Scheduled learning cycles with performance tracking
    - Real-time adaptation based on validated outcomes
    """

    def __init__(self, db_manager, config_manager):
        self.db_manager = db_manager
        self.config_manager = config_manager
        self.logger = logger.getChild(self.__class__.__name__)

        # Initialize dependencies for unified AI system
        self.historical_storage = HistoricalDataStorageV2_5(db_manager)

        # Initialize Unified AI Intelligence System
        self.unified_ai_system = get_unified_ai_intelligence_system(config_manager, db_manager)
        
        # Learning configuration
        self.learning_config = {
            'daily_learning_enabled': True,
            'weekly_deep_learning_enabled': True,
            'monthly_comprehensive_review_enabled': True,
            'real_time_adaptation_enabled': True,
            'min_confidence_for_auto_update': 0.8,
            'max_parameter_change_per_cycle': 0.15,
            'learning_history_retention_days': 90
        }
        
        # Learning history
        self.learning_history = []
        self.parameter_update_history = []
        
        # Performance tracking
        self.performance_baseline = {}
        self.current_performance = {}
        
        # Scheduler for automated learning
        self.scheduler_thread = None
        self.scheduler_running = False
    
    def initialize_adaptive_learning(self) -> bool:
        """Initialize the adaptive learning system."""
        try:
            self.logger.info("🚀 Initializing Adaptive Learning System v2.5")
            
            # Create necessary database tables
            self._create_learning_tables()
            
            # Load learning configuration
            self._load_learning_configuration()
            
            # Establish performance baseline
            self._establish_performance_baseline()
            
            # Start scheduled learning cycles
            self._start_learning_scheduler()
            
            self.logger.info("✅ Adaptive Learning System initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize adaptive learning: {e}")
            return False
    
    async def run_daily_learning_cycle(self, symbol: str = "SPY") -> UnifiedLearningResult:
        """Run daily learning cycle with focus on recent performance using Unified AI."""
        try:
            self.logger.info(f"📅 Running unified daily learning cycle for {symbol}")

            # Run unified learning cycle with 7-day lookback for daily adjustments
            result = await run_unified_learning_for_symbol(
                symbol, self.config_manager, self.db_manager, lookback_days=7
            )

            # Validate result against EOTS schemas
            if not result.eots_schema_compliance:
                self.logger.warning(f"⚠️ Learning result failed schema validation for {symbol}")

            # Store learning results with schema validation
            self._store_unified_learning_cycle_result(result, cycle_type="daily")

            # Apply high-confidence updates automatically
            applied_updates = self._apply_automatic_updates(result)

            # Update performance tracking with validation
            self._update_performance_tracking(result)

            self.logger.info(f"✅ Unified daily learning cycle completed. Applied {len(applied_updates)} updates")

            return result

        except Exception as e:
            self.logger.error(f"❌ Daily learning cycle failed: {e}")
            raise
    
    async def run_weekly_deep_learning(self, symbol: str = "SPY") -> UnifiedLearningResult:
        """Run weekly deep learning with comprehensive analysis using Unified AI."""
        try:
            self.logger.info(f"📊 Running unified weekly deep learning for {symbol}")

            # Run unified learning cycle with 30-day lookback for deeper insights
            result = await run_unified_learning_for_symbol(
                symbol, self.config_manager, self.db_manager, lookback_days=30
            )

            # Validate schema compliance
            if not result.eots_schema_compliance:
                self.logger.warning(f"⚠️ Weekly learning result failed schema validation for {symbol}")

            # Store learning results with validation
            self._store_unified_learning_cycle_result(result, cycle_type="weekly")

            # Validate against historical performance with schema compliance
            validation_result = self._validate_unified_learning_results(result)

            # Apply validated updates
            if validation_result['validation_passed'] and result.eots_schema_compliance:
                applied_updates = self._apply_validated_updates(result, validation_result)
                self.logger.info(f"✅ Unified weekly deep learning completed. Applied {len(applied_updates)} validated updates")
            else:
                self.logger.warning(f"⚠️ Weekly learning validation failed: {validation_result.get('reason', 'Schema validation failed')}")

            return result

        except Exception as e:
            self.logger.error(f"❌ Weekly deep learning failed: {e}")
            raise
    
    async def run_monthly_comprehensive_review(self, symbol: str = "SPY") -> Dict[str, Any]:
        """Run monthly comprehensive system review and optimization."""
        try:
            self.logger.info(f"🔍 Running monthly comprehensive review for {symbol}")
            
            # Run unified extended learning cycle with 90-day lookback
            learning_result = await run_unified_learning_for_symbol(
                symbol, self.config_manager, self.db_manager, lookback_days=90
            )
            
            # Comprehensive performance analysis
            performance_analysis = self._comprehensive_performance_analysis(symbol)
            
            # Parameter effectiveness review
            parameter_effectiveness = self._analyze_parameter_effectiveness()
            
            # Generate optimization recommendations
            optimization_recommendations = self._generate_optimization_recommendations(
                learning_result, performance_analysis, parameter_effectiveness
            )
            
            # Create comprehensive report
            comprehensive_report = {
                'timestamp': datetime.now().isoformat(),
                'symbol': symbol,
                'learning_result': learning_result,
                'performance_analysis': performance_analysis,
                'parameter_effectiveness': parameter_effectiveness,
                'optimization_recommendations': optimization_recommendations,
                'system_health_score': self._calculate_system_health_score(),
                'next_review_date': (datetime.now() + timedelta(days=30)).isoformat()
            }
            
            # Store comprehensive report
            self._store_comprehensive_report(comprehensive_report)
            
            self.logger.info("✅ Monthly comprehensive review completed")
            
            return comprehensive_report
            
        except Exception as e:
            self.logger.error(f"❌ Monthly comprehensive review failed: {e}")
            return {'error': str(e)}
    
    def _create_learning_tables(self):
        """Create database tables for learning system."""
        try:
            sql_statements = [
                # Historical analysis tables
                """
                CREATE TABLE IF NOT EXISTS daily_regime_snapshots (
                    snapshot_id SERIAL PRIMARY KEY,
                    symbol VARCHAR(20) NOT NULL,
                    date DATE NOT NULL,
                    primary_regime VARCHAR(100) NOT NULL,
                    regime_confidence_score DECIMAL(5,4) DEFAULT 0.0000,
                    regime_duration_minutes INTEGER DEFAULT 0,
                    regime_transition_count INTEGER DEFAULT 0,
                    secondary_regimes JSONB DEFAULT '[]',
                    market_conditions JSONB DEFAULT '{}',
                    volatility_environment VARCHAR(50),
                    flow_intensity_score DECIMAL(5,4) DEFAULT 0.0000,
                    created_at TIMESTAMPTZ DEFAULT NOW(),
                    UNIQUE(symbol, date)
                )
                """,
                """
                CREATE TABLE IF NOT EXISTS key_level_performance (
                    level_id SERIAL PRIMARY KEY,
                    symbol VARCHAR(20) NOT NULL,
                    date DATE NOT NULL,
                    level_price DECIMAL(10,4) NOT NULL,
                    level_type VARCHAR(50) NOT NULL,
                    level_source VARCHAR(50) NOT NULL,
                    hit_count INTEGER DEFAULT 0,
                    bounce_count INTEGER DEFAULT 0,
                    break_count INTEGER DEFAULT 0,
                    bounce_accuracy DECIMAL(5,4) DEFAULT 0.0000,
                    break_significance DECIMAL(5,4) DEFAULT 0.0000,
                    time_to_test_minutes INTEGER,
                    hold_duration_minutes INTEGER,
                    max_distance_from_level DECIMAL(10,4),
                    conviction_score DECIMAL(5,4) DEFAULT 0.0000,
                    market_regime_context VARCHAR(100),
                    created_at TIMESTAMPTZ DEFAULT NOW()
                )
                """,
                """
                CREATE TABLE IF NOT EXISTS flow_pattern_library (
                    pattern_id SERIAL PRIMARY KEY,
                    symbol VARCHAR(20) NOT NULL,
                    date DATE NOT NULL,
                    pattern_name VARCHAR(100) NOT NULL,
                    pattern_signature JSONB NOT NULL,
                    pattern_strength DECIMAL(5,4) DEFAULT 0.0000,
                    market_outcome VARCHAR(50),
                    follow_through_success BOOLEAN,
                    follow_through_magnitude DECIMAL(8,4),
                    regime_context VARCHAR(100),
                    volatility_environment VARCHAR(50),
                    time_horizon_minutes INTEGER,
                    success_rate DECIMAL(5,4) DEFAULT 0.0000,
                    sample_size INTEGER DEFAULT 1,
                    confidence_score DECIMAL(5,4) DEFAULT 0.0000,
                    created_at TIMESTAMPTZ DEFAULT NOW(),
                    last_updated TIMESTAMPTZ DEFAULT NOW()
                )
                """,
                """
                CREATE TABLE IF NOT EXISTS ai_performance_daily_summary (
                    summary_id SERIAL PRIMARY KEY,
                    date DATE NOT NULL,
                    symbol VARCHAR(20) NOT NULL,
                    total_predictions INTEGER DEFAULT 0,
                    correct_predictions INTEGER DEFAULT 0,
                    accuracy_rate DECIMAL(5,4) DEFAULT 0.0000,
                    avg_confidence_score DECIMAL(5,4) DEFAULT 0.0000,
                    regime_prediction_accuracy JSONB DEFAULT '{}',
                    prediction_type_performance JSONB DEFAULT '{}',
                    high_confidence_accuracy DECIMAL(5,4) DEFAULT 0.0000,
                    medium_confidence_accuracy DECIMAL(5,4) DEFAULT 0.0000,
                    low_confidence_accuracy DECIMAL(5,4) DEFAULT 0.0000,
                    created_at TIMESTAMPTZ DEFAULT NOW(),
                    UNIQUE(date, symbol)
                )
                """,
                # Learning system tables
                """
                CREATE TABLE IF NOT EXISTS learning_cycle_results (
                    id SERIAL PRIMARY KEY,
                    timestamp TIMESTAMPTZ NOT NULL,
                    symbol VARCHAR(20) NOT NULL,
                    cycle_type VARCHAR(50) NOT NULL,
                    lookback_days INTEGER NOT NULL,
                    insights_count INTEGER DEFAULT 0,
                    parameters_updated INTEGER DEFAULT 0,
                    confidence_score DECIMAL(5,4) DEFAULT 0.0000,
                    learning_summary TEXT,
                    result_data JSONB,
                    created_at TIMESTAMPTZ DEFAULT NOW()
                )
                """,
                """
                CREATE TABLE IF NOT EXISTS parameter_updates (
                    id SERIAL PRIMARY KEY,
                    parameter_name VARCHAR(100) NOT NULL,
                    old_value DECIMAL(10,6),
                    new_value DECIMAL(10,6) NOT NULL,
                    reasoning TEXT,
                    confidence_score DECIMAL(5,4) DEFAULT 0.0000,
                    applied_by VARCHAR(100) DEFAULT 'SelfLearningAgent',
                    applied_at TIMESTAMPTZ DEFAULT NOW(),
                    performance_impact DECIMAL(5,4),
                    rollback_at TIMESTAMPTZ NULL
                )
                """,
                """
                CREATE TABLE IF NOT EXISTS learning_performance_tracking (
                    id SERIAL PRIMARY KEY,
                    date DATE NOT NULL,
                    symbol VARCHAR(20) NOT NULL,
                    metric_name VARCHAR(100) NOT NULL,
                    metric_value DECIMAL(10,6) NOT NULL,
                    baseline_value DECIMAL(10,6),
                    improvement_percentage DECIMAL(8,4),
                    created_at TIMESTAMPTZ DEFAULT NOW(),
                    UNIQUE(date, symbol, metric_name)
                )
                """,
                # Market Dynamics Radar 15-Minute Data Table
                """
                CREATE TABLE IF NOT EXISTS market_dynamics_radar_15min (
                    radar_id SERIAL PRIMARY KEY,
                    symbol VARCHAR(20) NOT NULL,
                    timestamp TIMESTAMPTZ NOT NULL,
                    minute_interval INTEGER NOT NULL,
                    hour_of_day INTEGER NOT NULL,
                    trading_session VARCHAR(50) NOT NULL,

                    -- Core Market Forces (0-10 scale)
                    momentum_force DECIMAL(5,2) DEFAULT 0.00,
                    volatility_force DECIMAL(5,2) DEFAULT 0.00,
                    volume_force DECIMAL(5,2) DEFAULT 0.00,
                    sentiment_force DECIMAL(5,2) DEFAULT 0.00,
                    trend_force DECIMAL(5,2) DEFAULT 0.00,
                    support_resistance_force DECIMAL(5,2) DEFAULT 0.00,
                    options_flow_force DECIMAL(5,2) DEFAULT 0.00,
                    market_breadth_force DECIMAL(5,2) DEFAULT 0.00,

                    -- Dominant Force Analysis
                    dominant_force VARCHAR(50),
                    dominant_force_value DECIMAL(5,2) DEFAULT 0.00,
                    force_balance_score DECIMAL(5,4) DEFAULT 0.0000,

                    -- Underlying Metrics for Force Calculations
                    rsi_14 DECIMAL(6,2),
                    macd_signal DECIMAL(10,6),
                    price_change_percent DECIMAL(8,4),
                    volume_ratio DECIMAL(8,4),
                    vix_level DECIMAL(6,2),
                    put_call_ratio DECIMAL(8,4),
                    call_volume BIGINT DEFAULT 0,
                    put_volume BIGINT DEFAULT 0,
                    advance_decline_ratio DECIMAL(8,4),
                    new_highs_lows_ratio DECIMAL(8,4),

                    -- EOTS Integration
                    vapi_fa_z_score DECIMAL(8,4),
                    dwfd_z_score DECIMAL(8,4),
                    tw_laf_z_score DECIMAL(8,4),
                    gib_oi_based DECIMAL(12,2),
                    a_dag_value DECIMAL(12,2),
                    vri_2_0_value DECIMAL(12,2),

                    -- Market Context
                    market_regime VARCHAR(100),
                    volatility_environment VARCHAR(50),
                    trading_volume BIGINT DEFAULT 0,
                    price_level DECIMAL(10,4),

                    -- Radar Performance Metrics
                    prediction_accuracy DECIMAL(5,4),
                    signal_strength DECIMAL(5,4) DEFAULT 0.0000,
                    confidence_score DECIMAL(5,4) DEFAULT 0.0000,

                    created_at TIMESTAMPTZ DEFAULT NOW(),

                    -- Unique constraint for 15-minute intervals
                    UNIQUE(symbol, timestamp, minute_interval)
                )
                """,
                # Market Dynamics Radar Hourly Data Table
                """
                CREATE TABLE IF NOT EXISTS market_dynamics_radar_hourly (
                    radar_id SERIAL PRIMARY KEY,
                    symbol VARCHAR(20) NOT NULL,
                    timestamp TIMESTAMPTZ NOT NULL,
                    hour_of_day INTEGER NOT NULL,
                    trading_session VARCHAR(50) NOT NULL,

                    -- Core Market Forces (0-10 scale)
                    momentum_force DECIMAL(5,2) DEFAULT 0.00,
                    volatility_force DECIMAL(5,2) DEFAULT 0.00,
                    volume_force DECIMAL(5,2) DEFAULT 0.00,
                    sentiment_force DECIMAL(5,2) DEFAULT 0.00,
                    trend_force DECIMAL(5,2) DEFAULT 0.00,
                    support_resistance_force DECIMAL(5,2) DEFAULT 0.00,
                    options_flow_force DECIMAL(5,2) DEFAULT 0.00,
                    market_breadth_force DECIMAL(5,2) DEFAULT 0.00,

                    -- Dominant Force Analysis
                    dominant_force VARCHAR(50),
                    dominant_force_value DECIMAL(5,2) DEFAULT 0.00,
                    force_balance_score DECIMAL(5,4) DEFAULT 0.0000,

                    -- Underlying Metrics for Force Calculations
                    rsi_14 DECIMAL(6,2),
                    macd_signal DECIMAL(10,6),
                    price_change_percent DECIMAL(8,4),
                    volume_ratio DECIMAL(8,4),
                    vix_level DECIMAL(6,2),
                    put_call_ratio DECIMAL(8,4),
                    call_volume BIGINT DEFAULT 0,
                    put_volume BIGINT DEFAULT 0,
                    advance_decline_ratio DECIMAL(8,4),
                    new_highs_lows_ratio DECIMAL(8,4),

                    -- EOTS Integration
                    vapi_fa_z_score DECIMAL(8,4),
                    dwfd_z_score DECIMAL(8,4),
                    tw_laf_z_score DECIMAL(8,4),
                    gib_oi_based DECIMAL(12,2),
                    a_dag_value DECIMAL(12,2),
                    vri_2_0_value DECIMAL(12,2),

                    -- Market Context
                    market_regime VARCHAR(100),
                    volatility_environment VARCHAR(50),
                    trading_volume BIGINT DEFAULT 0,
                    price_level DECIMAL(10,4),

                    -- Radar Performance Metrics
                    prediction_accuracy DECIMAL(5,4),
                    signal_strength DECIMAL(5,4) DEFAULT 0.0000,
                    confidence_score DECIMAL(5,4) DEFAULT 0.0000,

                    created_at TIMESTAMPTZ DEFAULT NOW(),

                    -- Constraints
                    CONSTRAINT chk_forces_range CHECK (
                        momentum_force >= 0 AND momentum_force <= 10 AND
                        volatility_force >= 0 AND volatility_force <= 10 AND
                        volume_force >= 0 AND volume_force <= 10 AND
                        sentiment_force >= 0 AND sentiment_force <= 10 AND
                        trend_force >= 0 AND trend_force <= 10 AND
                        support_resistance_force >= 0 AND support_resistance_force <= 10 AND
                        options_flow_force >= 0 AND options_flow_force <= 10 AND
                        market_breadth_force >= 0 AND market_breadth_force <= 10
                    ),
                    CONSTRAINT chk_scores_range CHECK (
                        force_balance_score >= 0 AND force_balance_score <= 1 AND
                        prediction_accuracy >= 0 AND prediction_accuracy <= 1 AND
                        signal_strength >= 0 AND signal_strength <= 1 AND
                        confidence_score >= 0 AND confidence_score <= 1
                    ),

                    -- Unique constraint for hourly data
                    UNIQUE(symbol, timestamp, hour_of_day)
                )
                """
            ]

            # Create indexes for optimal performance
            index_statements = [
                "CREATE INDEX IF NOT EXISTS idx_regime_snapshots_symbol_date ON daily_regime_snapshots(symbol, date)",
                "CREATE INDEX IF NOT EXISTS idx_regime_snapshots_regime ON daily_regime_snapshots(primary_regime)",
                "CREATE INDEX IF NOT EXISTS idx_key_level_symbol_date ON key_level_performance(symbol, date)",
                "CREATE INDEX IF NOT EXISTS idx_key_level_type ON key_level_performance(level_type)",
                "CREATE INDEX IF NOT EXISTS idx_flow_pattern_symbol_date ON flow_pattern_library(symbol, date)",
                "CREATE INDEX IF NOT EXISTS idx_flow_pattern_name ON flow_pattern_library(pattern_name)",
                "CREATE INDEX IF NOT EXISTS idx_ai_performance_date ON ai_performance_daily_summary(date)",
                "CREATE INDEX IF NOT EXISTS idx_learning_cycle_timestamp ON learning_cycle_results(timestamp)",
                "CREATE INDEX IF NOT EXISTS idx_parameter_updates_name ON parameter_updates(parameter_name)",
                "CREATE INDEX IF NOT EXISTS idx_market_radar_symbol_timestamp ON market_dynamics_radar_hourly(symbol, timestamp)",
                "CREATE INDEX IF NOT EXISTS idx_market_radar_hour ON market_dynamics_radar_hourly(hour_of_day)",
                "CREATE INDEX IF NOT EXISTS idx_market_radar_dominant_force ON market_dynamics_radar_hourly(dominant_force)",
                "CREATE INDEX IF NOT EXISTS idx_market_radar_regime ON market_dynamics_radar_hourly(market_regime)",
                "CREATE INDEX IF NOT EXISTS idx_market_radar_session ON market_dynamics_radar_hourly(trading_session)"
            ]

            cursor = self.db_manager._conn.cursor()

            # Create tables
            for sql in sql_statements:
                cursor.execute(sql)

            # Create indexes
            for index_sql in index_statements:
                cursor.execute(index_sql)

            self.logger.info("📊 Learning system database tables and indexes created/verified")

        except Exception as e:
            self.logger.error(f"Failed to create learning tables: {e}")
            raise
    
    def _load_learning_configuration(self):
        """Load learning configuration from consolidated pydantic AI config."""
        try:
            # Load from consolidated pydantic AI config
            config_path = Path("config/pydantic_ai_config.json")
            if config_path.exists():
                with open(config_path, 'r') as f:
                    full_config = json.load(f)
                    adaptive_config = full_config.get('adaptive_learning_settings', {})
                    if adaptive_config:
                        self.learning_config.update(adaptive_config)
                        self.logger.info("📋 Learning configuration loaded from consolidated pydantic AI config")
                    else:
                        self.logger.warning("📋 No adaptive_learning_settings found in pydantic AI config")
            else:
                self.logger.warning("📋 Pydantic AI config file not found")

        except Exception as e:
            self.logger.warning(f"Failed to load learning configuration: {e}")
    
    def _establish_performance_baseline(self):
        """Establish performance baseline for comparison."""
        try:
            # Query recent performance metrics
            sql = """
            SELECT 
                AVG(accuracy_rate) as avg_accuracy,
                AVG(total_predictions) as avg_predictions_per_day,
                COUNT(*) as days_analyzed
            FROM ai_performance_daily_summary 
            WHERE date >= CURRENT_DATE - INTERVAL '30 days'
            """
            
            cursor = self.db_manager._conn.cursor()
            cursor.execute(sql)
            result = cursor.fetchone()
            
            if result and result['days_analyzed'] > 0:
                self.performance_baseline = {
                    'ai_accuracy': result['avg_accuracy'] or 0.0,
                    'predictions_per_day': result['avg_predictions_per_day'] or 0.0,
                    'baseline_established_at': datetime.now().isoformat()
                }
                self.logger.info(f"📊 Performance baseline established: {self.performance_baseline}")
            else:
                self.logger.warning("⚠️ Insufficient data to establish performance baseline")
                
        except Exception as e:
            self.logger.error(f"Failed to establish performance baseline: {e}")
    
    def _start_learning_scheduler(self):
        """Start the automated learning scheduler."""
        try:
            if self.learning_config['daily_learning_enabled']:
                schedule.every().day.at("02:00").do(self._scheduled_daily_learning)

            if self.learning_config['weekly_deep_learning_enabled']:
                schedule.every().sunday.at("03:00").do(self._scheduled_weekly_learning)

            if self.learning_config['monthly_comprehensive_review_enabled']:
                schedule.every().month.do(self._scheduled_monthly_review)

            # Schedule 15-minute Market Dynamics radar data collection during market hours
            schedule.every(15).minutes.do(self._scheduled_radar_data_collection)

            # Start scheduler thread
            self.scheduler_running = True
            self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
            self.scheduler_thread.start()

            self.logger.info("⏰ Learning scheduler started")
            self.logger.info("📡 Market Dynamics radar data collection scheduled every 15 minutes")

        except Exception as e:
            self.logger.error(f"Failed to start learning scheduler: {e}")
    
    def _run_scheduler(self):
        """Run the learning scheduler."""
        while self.scheduler_running:
            schedule.run_pending()
            threading.Event().wait(60)  # Check every minute
    
    def _scheduled_daily_learning(self):
        """Scheduled daily learning execution."""
        try:
            asyncio.run(self.run_daily_learning_cycle())
        except Exception as e:
            self.logger.error(f"Scheduled daily learning failed: {e}")
    
    def _scheduled_weekly_learning(self):
        """Scheduled weekly learning execution."""
        try:
            asyncio.run(self.run_weekly_deep_learning())
        except Exception as e:
            self.logger.error(f"Scheduled weekly learning failed: {e}")
    
    def _scheduled_monthly_review(self):
        """Scheduled monthly review execution."""
        try:
            asyncio.run(self.run_monthly_comprehensive_review())
        except Exception as e:
            self.logger.error(f"Scheduled monthly review failed: {e}")

    def _scheduled_radar_data_collection(self):
        """Scheduled Market Dynamics radar data collection every 15 minutes."""
        try:
            # Only collect during market hours and pre/post market
            current_time = datetime.now()
            session = self._determine_trading_session(current_time)

            if session in ["PRE_MARKET", "REGULAR_HOURS", "AFTER_HOURS"]:
                # Get current bundle data from global scope if available
                if hasattr(builtins, 'current_bundle_data'):
                    bundle = getattr(builtins, 'current_bundle_data', None)
                    if bundle:
                        self.capture_market_dynamics_radar_data(bundle)
                        self.logger.debug(f"📡 Radar data collected at {current_time.strftime('%H:%M')} during {session}")
                else:
                    self.logger.debug(f"📡 Skipping radar collection - no bundle data available")
            else:
                self.logger.debug(f"📡 Skipping radar collection - market closed ({session})")

        except Exception as e:
            self.logger.error(f"Scheduled radar data collection failed: {e}")
    
    def stop_learning_scheduler(self):
        """Stop the learning scheduler."""
        self.scheduler_running = False
        if self.scheduler_thread:
            self.scheduler_thread.join(timeout=5)
        self.logger.info("⏹️ Learning scheduler stopped")

    def _store_learning_cycle_result(self, result: UnifiedLearningResult, cycle_type: str):
        """Store learning cycle results in database."""
        try:
            sql = """
            INSERT INTO learning_cycle_results
            (timestamp, symbol, cycle_type, lookback_days, insights_count,
             parameters_updated, confidence_score, learning_summary, result_data)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """

            cursor = self.db_manager._conn.cursor()
            cursor.execute(sql, (
                result.timestamp,
                result.symbol,
                cycle_type,
                30,  # Default lookback days
                len(result.new_insights),
                len(result.parameter_optimizations),
                result.confidence_evolution,
                f"Unified Learning: {len(result.new_insights)} insights, {len(result.pattern_adaptations)} adaptations",
                json.dumps(result.model_dump())
            ))

        except Exception as e:
            self.logger.error(f"Failed to store learning cycle result: {e}")

    def _apply_automatic_updates(self, result: UnifiedLearningResult) -> List[str]:
        """Apply automatic parameter updates for high-confidence insights."""
        applied_updates = []

        try:
            min_confidence = self.learning_config['min_confidence_for_auto_update']

            # Apply parameter optimizations from unified learning
            for param_name, new_value in result.parameter_optimizations.items():
                if result.confidence_evolution >= min_confidence:
                    # Apply the parameter update
                    success = self._update_parameter_safely(
                        param_name,
                        new_value,
                        f"Unified learning optimization: {result.confidence_evolution:.2f} confidence",
                        result.confidence_evolution
                    )

                    if success:
                        applied_updates.append(param_name)
                        self.logger.info(f"✅ Auto-applied update: {param_name} = {new_value}")

        except Exception as e:
            self.logger.error(f"Failed to apply automatic updates: {e}")

        return applied_updates

    def capture_market_dynamics_radar_data(self, bundle: 'FinalAnalysisBundleV2_5') -> bool:
        """Capture Market Dynamics radar data every 15 minutes for fine-tuning."""
        try:
            from datetime import datetime

            symbol = bundle.target_symbol
            current_time = datetime.now()

            # Calculate 15-minute interval (0, 15, 30, 45)
            minute_interval = (current_time.minute // 15) * 15
            hour_of_day = current_time.hour

            # Determine trading session
            trading_session = self._determine_trading_session(current_time)

            # Extract underlying data
            und_data = bundle.processed_data_bundle.underlying_data_enriched
            metrics = und_data.model_dump() if und_data else {}

            # Calculate market forces using existing functions
            momentum_force = self._calculate_momentum_force(metrics)
            volatility_force = self._calculate_volatility_force(metrics)
            volume_force = self._calculate_volume_force(metrics)
            sentiment_force = self._calculate_sentiment_force(metrics)
            trend_force = self._calculate_trend_force(metrics)
            sr_force = self._calculate_sr_force(metrics)
            options_flow_force = self._calculate_options_flow_force(metrics)
            breadth_force = self._calculate_market_breadth_force(metrics)

            # Determine dominant force
            forces = {
                'Momentum': momentum_force,
                'Volatility': volatility_force,
                'Volume': volume_force,
                'Sentiment': sentiment_force,
                'Trend': trend_force,
                'Support/Resistance': sr_force,
                'Options Flow': options_flow_force,
                'Market Breadth': breadth_force
            }

            dominant_force = max(forces, key=forces.get)
            dominant_force_value = forces[dominant_force]

            # Calculate force balance score (how balanced vs concentrated the forces are)
            force_values = list(forces.values())
            force_balance_score = 1.0 - (max(force_values) - min(force_values)) / 10.0

            # Calculate signal strength and confidence
            signal_strength = max(force_values) / 10.0
            confidence_score = min(force_balance_score + (signal_strength * 0.5), 1.0)

            # Prepare radar data
            radar_data = {
                'symbol': symbol,
                'timestamp': current_time,
                'minute_interval': minute_interval,
                'hour_of_day': hour_of_day,
                'trading_session': trading_session,

                # Core forces
                'momentum_force': momentum_force,
                'volatility_force': volatility_force,
                'volume_force': volume_force,
                'sentiment_force': sentiment_force,
                'trend_force': trend_force,
                'support_resistance_force': sr_force,
                'options_flow_force': options_flow_force,
                'market_breadth_force': breadth_force,

                # Dominant force analysis
                'dominant_force': dominant_force,
                'dominant_force_value': dominant_force_value,
                'force_balance_score': force_balance_score,

                # Underlying metrics
                'rsi_14': metrics.get('rsi_14', None),
                'macd_signal': metrics.get('macd_signal', None),
                'price_change_percent': metrics.get('price_change_percent', None),
                'volume_ratio': metrics.get('volume_ratio', None),
                'vix_level': metrics.get('vix', None),
                'put_call_ratio': metrics.get('put_call_ratio', None),
                'call_volume': metrics.get('call_volume', 0),
                'put_volume': metrics.get('put_volume', 0),
                'advance_decline_ratio': metrics.get('advance_decline_ratio', None),
                'new_highs_lows_ratio': metrics.get('new_highs_lows_ratio', None),

                # EOTS integration
                'vapi_fa_z_score': metrics.get('vapi_fa_z_score_und', None),
                'dwfd_z_score': metrics.get('dwfd_z_score_und', None),
                'tw_laf_z_score': metrics.get('tw_laf_z_score_und', None),
                'gib_oi_based': metrics.get('gib_oi_based_und', None),
                'a_dag_value': metrics.get('a_dag_und', None),
                'vri_2_0_value': metrics.get('vri_2_0_und', None),

                # Market context
                'market_regime': getattr(und_data, 'current_market_regime_v2_5', None),
                'volatility_environment': self._determine_volatility_environment(und_data),
                'trading_volume': metrics.get('total_volume', 0),
                'price_level': metrics.get('underlying_price', None),

                # Performance metrics
                'prediction_accuracy': None,  # Will be calculated later
                'signal_strength': signal_strength,
                'confidence_score': confidence_score
            }

            # Store in database
            sql = """
            INSERT INTO market_dynamics_radar_15min
            (symbol, timestamp, minute_interval, hour_of_day, trading_session,
             momentum_force, volatility_force, volume_force, sentiment_force,
             trend_force, support_resistance_force, options_flow_force, market_breadth_force,
             dominant_force, dominant_force_value, force_balance_score,
             rsi_14, macd_signal, price_change_percent, volume_ratio, vix_level, put_call_ratio,
             call_volume, put_volume, advance_decline_ratio, new_highs_lows_ratio,
             vapi_fa_z_score, dwfd_z_score, tw_laf_z_score, gib_oi_based, a_dag_value, vri_2_0_value,
             market_regime, volatility_environment, trading_volume, price_level,
             prediction_accuracy, signal_strength, confidence_score)
            VALUES (%(symbol)s, %(timestamp)s, %(minute_interval)s, %(hour_of_day)s, %(trading_session)s,
                    %(momentum_force)s, %(volatility_force)s, %(volume_force)s, %(sentiment_force)s,
                    %(trend_force)s, %(support_resistance_force)s, %(options_flow_force)s, %(market_breadth_force)s,
                    %(dominant_force)s, %(dominant_force_value)s, %(force_balance_score)s,
                    %(rsi_14)s, %(macd_signal)s, %(price_change_percent)s, %(volume_ratio)s, %(vix_level)s, %(put_call_ratio)s,
                    %(call_volume)s, %(put_volume)s, %(advance_decline_ratio)s, %(new_highs_lows_ratio)s,
                    %(vapi_fa_z_score)s, %(dwfd_z_score)s, %(tw_laf_z_score)s, %(gib_oi_based)s, %(a_dag_value)s, %(vri_2_0_value)s,
                    %(market_regime)s, %(volatility_environment)s, %(trading_volume)s, %(price_level)s,
                    %(prediction_accuracy)s, %(signal_strength)s, %(confidence_score)s)
            ON CONFLICT (symbol, timestamp, minute_interval)
            DO UPDATE SET
                momentum_force = EXCLUDED.momentum_force,
                volatility_force = EXCLUDED.volatility_force,
                volume_force = EXCLUDED.volume_force,
                sentiment_force = EXCLUDED.sentiment_force,
                trend_force = EXCLUDED.trend_force,
                support_resistance_force = EXCLUDED.support_resistance_force,
                options_flow_force = EXCLUDED.options_flow_force,
                market_breadth_force = EXCLUDED.market_breadth_force,
                dominant_force = EXCLUDED.dominant_force,
                dominant_force_value = EXCLUDED.dominant_force_value,
                force_balance_score = EXCLUDED.force_balance_score,
                signal_strength = EXCLUDED.signal_strength,
                confidence_score = EXCLUDED.confidence_score
            """

            cursor = self.db_manager._conn.cursor()
            cursor.execute(sql, radar_data)

            self.logger.info(f"📡 Market Dynamics radar data captured for {symbol} at {current_time.strftime('%H:%M')} | Dominant: {dominant_force} ({dominant_force_value:.1f})")

            return True

        except Exception as e:
            self.logger.error(f"Failed to capture Market Dynamics radar data: {e}")
            return False

    def _update_parameter_safely(self, param_name: str, new_value: float, reasoning: str, confidence: float) -> bool:
        """Safely update a parameter with validation and rollback capability."""
        try:
            # Get current value (placeholder - adapt to your config system)
            current_value = self._get_current_parameter_value(param_name)

            # Validate change magnitude
            if current_value != 0:
                change_ratio = abs(new_value - current_value) / abs(current_value)
                max_change = self.learning_config['max_parameter_change_per_cycle']

                if change_ratio > max_change:
                    self.logger.warning(f"Parameter change too large: {change_ratio:.1%} > {max_change:.1%}")
                    return False

            # Store parameter update record
            sql = """
            INSERT INTO parameter_updates
            (parameter_name, old_value, new_value, reasoning, confidence_score, performance_impact)
            VALUES (%s, %s, %s, %s, %s, %s)
            """

            cursor = self.db_manager._conn.cursor()
            cursor.execute(sql, (param_name, current_value, new_value, reasoning, confidence, 0.0))

            # TODO: Actually update your config files/system here
            # This would depend on your specific configuration management

            return True

        except Exception as e:
            self.logger.error(f"Failed to update parameter {param_name}: {e}")
            return False

    def _get_current_parameter_value(self, param_name: str) -> float:
        """Get current parameter value from config system."""
        # Placeholder - adapt to your actual config system
        default_values = {
            'ai_confidence_threshold': 0.7,
            'vapi_fa_threshold': 1.5,
            'dwfd_threshold': 1.5,
            'tw_laf_threshold': 1.5,
            'regime_confidence_threshold': 0.7
        }
        return default_values.get(param_name, 0.0)

    def _validate_learning_results(self, result: UnifiedLearningResult) -> Dict[str, Any]:
        """Validate learning results against historical performance."""
        try:
            validation_result = {
                'validation_passed': True,
                'reason': '',
                'confidence_score': result.confidence_evolution,
                'risk_assessment': 'LOW'
            }

            # Check if confidence is sufficient
            if result.confidence_evolution < 0.6:
                validation_result['validation_passed'] = False
                validation_result['reason'] = f"Low confidence score: {result.confidence_evolution}"
                return validation_result

            # Check if insights are reasonable
            if len(result.new_insights) == 0:
                validation_result['validation_passed'] = False
                validation_result['reason'] = "No new insights generated"
                return validation_result

            # Check schema compliance
            if not result.eots_schema_compliance:
                validation_result['validation_passed'] = False
                validation_result['reason'] = "Failed EOTS schema compliance"
                return validation_result

            return validation_result

        except Exception as e:
            self.logger.error(f"Failed to validate learning results: {e}")
            return {'validation_passed': False, 'reason': f'Validation error: {e}'}

    def _apply_validated_updates(self, result: UnifiedLearningResult, validation: Dict[str, Any]) -> List[str]:
        """Apply parameter updates that have passed validation."""
        applied_updates = []

        try:
            # Apply parameter optimizations that passed validation
            for param_name, new_value in result.parameter_optimizations.items():
                if result.confidence_evolution >= 0.6:
                    success = self._update_parameter_safely(
                        param_name,
                        new_value,
                        f"Validated unified learning update: {result.confidence_evolution:.2f} confidence",
                        result.confidence_evolution
                    )

                    if success:
                        applied_updates.append(param_name)

        except Exception as e:
            self.logger.error(f"Failed to apply validated updates: {e}")

        return applied_updates

    def _update_performance_tracking(self, result: UnifiedLearningResult):
        """Update performance tracking metrics."""
        try:
            # Store performance improvements from validation data
            performance_data = result.performance_validation
            if isinstance(performance_data, dict):
                for metric, improvement in performance_data.items():
                    sql = """
                    INSERT INTO learning_performance_tracking
                    (date, symbol, metric_name, metric_value, improvement_percentage)
                    VALUES (%s, %s, %s, %s, %s)
                    ON CONFLICT (date, symbol, metric_name)
                    DO UPDATE SET
                        metric_value = EXCLUDED.metric_value,
                        improvement_percentage = EXCLUDED.improvement_percentage
                    """

                    cursor = self.db_manager._conn.cursor()
                    if isinstance(improvement, (int, float)):
                        cursor.execute(sql, (
                            datetime.now().date(),
                            result.symbol,
                            metric,
                            improvement,
                            improvement * 100 if improvement != 0 else 0  # Convert to percentage
                        ))

        except Exception as e:
            self.logger.error(f"Failed to update performance tracking: {e}")

    def get_learning_status(self) -> Dict[str, Any]:
        """Get current learning system status and recent performance."""
        try:
            # Get recent learning cycles
            sql = """
            SELECT cycle_type, timestamp, confidence_score, parameters_updated
            FROM learning_cycle_results
            ORDER BY timestamp DESC
            LIMIT 10
            """

            cursor = self.db_manager._conn.cursor()
            cursor.execute(sql)
            recent_cycles = [dict(row) for row in cursor.fetchall()]

            # Get recent parameter updates
            sql = """
            SELECT parameter_name, new_value, confidence_score, applied_at
            FROM parameter_updates
            WHERE rollback_at IS NULL
            ORDER BY applied_at DESC
            LIMIT 10
            """

            cursor.execute(sql)
            recent_updates = [dict(row) for row in cursor.fetchall()]

            return {
                'learning_system_active': self.scheduler_running,
                'recent_learning_cycles': recent_cycles,
                'recent_parameter_updates': recent_updates,
                'performance_baseline': self.performance_baseline,
                'learning_config': self.learning_config,
                'next_scheduled_cycles': {
                    'daily': 'Every day at 02:00',
                    'weekly': 'Every Sunday at 03:00',
                    'monthly': 'First day of each month',
                    'radar_data_collection': 'Every 15 minutes during market hours'
                }
            }

        except Exception as e:
            self.logger.error(f"Failed to get learning status: {e}")
            return {'error': str(e)}

    def _determine_trading_session(self, timestamp) -> str:
        """Determine trading session based on timestamp."""
        hour = timestamp.hour
        minute = timestamp.minute

        # Convert to minutes since midnight
        total_minutes = hour * 60 + minute

        # Define session times (EST/EDT)
        pre_market_start = 4 * 60  # 4:00 AM
        market_open = 9 * 60 + 30  # 9:30 AM
        market_close = 16 * 60     # 4:00 PM
        after_hours_end = 20 * 60  # 8:00 PM

        if total_minutes < pre_market_start:
            return "OVERNIGHT"
        elif total_minutes < market_open:
            return "PRE_MARKET"
        elif total_minutes < market_close:
            return "REGULAR_HOURS"
        elif total_minutes < after_hours_end:
            return "AFTER_HOURS"
        else:
            return "OVERNIGHT"

    # Market Force Calculation Functions (adapted from AI dashboard)
    def _calculate_momentum_force(self, metrics: dict) -> float:
        """Calculate momentum force (0-10 scale)."""
        try:
            rsi = metrics.get('rsi_14', 50)
            macd_signal = metrics.get('macd_signal', 0)
            price_change = metrics.get('price_change_percent', 0)

            # Normalize to 0-10 scale
            rsi_score = abs(rsi - 50) / 5  # 0-10 based on deviation from neutral
            macd_score = min(abs(macd_signal) * 100, 10)  # Scale MACD signal
            price_score = min(abs(price_change) * 2, 10)  # Scale price change

            return min((rsi_score + macd_score + price_score) / 3, 10)
        except:
            return 5.0

    def _calculate_volatility_force(self, metrics: dict) -> float:
        """Calculate volatility force (0-10 scale)."""
        try:
            vix = metrics.get('vix', 20)
            atr = metrics.get('atr_14', 0)

            # Normalize to 0-10 scale
            vix_score = min(vix / 3, 10)  # VIX scale
            atr_score = min(atr * 100, 10)  # ATR scale

            return min((vix_score + atr_score) / 2, 10)
        except:
            return 5.0

    def _calculate_volume_force(self, metrics: dict) -> float:
        """Calculate volume force (0-10 scale)."""
        try:
            volume_ratio = metrics.get('volume_ratio', 1.0)
            total_volume = metrics.get('total_volume', 0)

            # Normalize to 0-10 scale
            ratio_score = min(abs(volume_ratio - 1.0) * 10, 10)
            volume_score = min(total_volume / 1000000, 10)  # Volume in millions

            return min((ratio_score + volume_score) / 2, 10)
        except:
            return 5.0

    def _calculate_sentiment_force(self, metrics: dict) -> float:
        """Calculate sentiment force (0-10 scale)."""
        try:
            put_call_ratio = metrics.get('put_call_ratio', 1.0)
            vix_level = metrics.get('vix', 20)

            # Normalize to 0-10 scale
            pc_score = min(abs(put_call_ratio - 1.0) * 20, 10)
            vix_score = min(vix_level / 3, 10)

            return min((pc_score + vix_score) / 2, 10)
        except:
            return 5.0

    def _calculate_trend_force(self, metrics: dict) -> float:
        """Calculate trend force (0-10 scale)."""
        try:
            # Use EOTS metrics for trend
            vapi_fa = abs(metrics.get('vapi_fa_z_score_und', 0.0))
            dwfd = abs(metrics.get('dwfd_z_score_und', 0.0))

            # Normalize to 0-10 scale
            trend_score = min((vapi_fa + dwfd) / 0.6, 10)  # Scale z-scores

            return trend_score
        except:
            return 5.0

    def _calculate_sr_force(self, metrics: dict) -> float:
        """Calculate support/resistance force (0-10 scale)."""
        try:
            # Use A-DAG and GIB for S/R levels
            a_dag = abs(metrics.get('a_dag_und', 0.0))
            gib = abs(metrics.get('gib_oi_based_und', 0.0))

            # Normalize to 0-10 scale
            a_dag_score = min(a_dag / 25000, 10)
            gib_score = min(gib / 50000, 10)

            return min((a_dag_score + gib_score) / 2, 10)
        except:
            return 5.0

    def _calculate_options_flow_force(self, metrics: dict) -> float:
        """Calculate options flow force (0-10 scale)."""
        try:
            call_volume = metrics.get('call_volume', 0)
            put_volume = metrics.get('put_volume', 0)
            total_volume = call_volume + put_volume

            if total_volume == 0:
                return 5.0

            # Normalize to 0-10 scale
            flow_imbalance = abs(call_volume - put_volume) / total_volume
            flow_score = min(flow_imbalance * 20, 10)

            return flow_score
        except:
            return 5.0

    def _calculate_market_breadth_force(self, metrics: dict) -> float:
        """Calculate market breadth force (0-10 scale)."""
        try:
            advance_decline = metrics.get('advance_decline_ratio', 1.0)
            new_highs_lows = metrics.get('new_highs_lows_ratio', 1.0)

            # Normalize to 0-10 scale
            ad_score = min(abs(advance_decline - 1.0) * 20, 10)
            hl_score = min(abs(new_highs_lows - 1.0) * 20, 10)

            return min((ad_score + hl_score) / 2, 10)
        except:
            return 5.0

    def get_radar_performance_analysis(self, symbol: str = "SPY", days: int = 7) -> Dict[str, Any]:
        """Get Market Dynamics radar performance analysis for fine-tuning."""
        try:
            sql = """
            SELECT
                DATE(timestamp) as date,
                trading_session,
                AVG(momentum_force) as avg_momentum,
                AVG(volatility_force) as avg_volatility,
                AVG(volume_force) as avg_volume,
                AVG(sentiment_force) as avg_sentiment,
                AVG(trend_force) as avg_trend,
                AVG(support_resistance_force) as avg_sr,
                AVG(options_flow_force) as avg_options_flow,
                AVG(market_breadth_force) as avg_breadth,
                dominant_force,
                COUNT(*) as data_points,
                AVG(force_balance_score) as avg_balance,
                AVG(signal_strength) as avg_signal_strength,
                AVG(confidence_score) as avg_confidence
            FROM market_dynamics_radar_15min
            WHERE symbol = %s
            AND timestamp >= CURRENT_DATE - INTERVAL '%s days'
            GROUP BY DATE(timestamp), trading_session, dominant_force
            ORDER BY date DESC, trading_session
            """

            cursor = self.db_manager._conn.cursor()
            cursor.execute(sql, (symbol, days))
            results = cursor.fetchall()

            # Analyze patterns
            analysis = {
                'daily_patterns': [dict(row) for row in results],
                'dominant_force_frequency': {},
                'session_analysis': {},
                'optimization_insights': []
            }

            # Calculate dominant force frequency
            for row in results:
                force = row['dominant_force']
                if force not in analysis['dominant_force_frequency']:
                    analysis['dominant_force_frequency'][force] = 0
                analysis['dominant_force_frequency'][force] += row['data_points']

            # Session-based analysis
            sessions = ['PRE_MARKET', 'REGULAR_HOURS', 'AFTER_HOURS']
            for session in sessions:
                session_data = [row for row in results if row['trading_session'] == session]
                if session_data:
                    analysis['session_analysis'][session] = {
                        'avg_signal_strength': sum(row['avg_signal_strength'] or 0 for row in session_data) / len(session_data),
                        'avg_confidence': sum(row['avg_confidence'] or 0 for row in session_data) / len(session_data),
                        'data_points': sum(row['data_points'] for row in session_data)
                    }

            # Generate optimization insights
            if analysis['session_analysis']:
                best_session = max(analysis['session_analysis'].items(),
                                 key=lambda x: x[1]['avg_signal_strength'])
                analysis['optimization_insights'].append(
                    f"Highest signal strength during {best_session[0]} session ({best_session[1]['avg_signal_strength']:.2f})"
                )

            if analysis['dominant_force_frequency']:
                most_common_force = max(analysis['dominant_force_frequency'].items(), key=lambda x: x[1])
                analysis['optimization_insights'].append(
                    f"Most dominant force: {most_common_force[0]} ({most_common_force[1]} data points)"
                )

            return analysis

        except Exception as e:
            self.logger.error(f"Failed to get radar performance analysis: {e}")
            return {'error': str(e)}

    def optimize_radar_thresholds(self, symbol: str = "SPY", days: int = 30) -> Dict[str, Any]:
        """Analyze radar data to optimize force calculation thresholds."""
        try:
            sql = """
            SELECT
                momentum_force, volatility_force, volume_force, sentiment_force,
                trend_force, support_resistance_force, options_flow_force, market_breadth_force,
                rsi_14, macd_signal, price_change_percent, volume_ratio, vix_level, put_call_ratio,
                vapi_fa_z_score, dwfd_z_score, tw_laf_z_score, gib_oi_based, a_dag_value,
                signal_strength, confidence_score, market_regime
            FROM market_dynamics_radar_15min
            WHERE symbol = %s
            AND timestamp >= CURRENT_DATE - INTERVAL '%s days'
            AND signal_strength IS NOT NULL
            ORDER BY timestamp DESC
            """

            cursor = self.db_manager._conn.cursor()
            cursor.execute(sql, (symbol, days))
            data = cursor.fetchall()

            if len(data) < 50:  # Need sufficient data for optimization
                return {'error': 'Insufficient data for optimization', 'data_points': len(data)}

            optimization_results = {
                'data_points_analyzed': len(data),
                'current_performance': {
                    'avg_signal_strength': sum(row['signal_strength'] or 0 for row in data) / len(data),
                    'avg_confidence': sum(row['confidence_score'] or 0 for row in data) / len(data)
                },
                'force_correlations': {},
                'threshold_recommendations': {},
                'regime_specific_insights': {}
            }

            # Analyze force correlations with signal strength
            forces = ['momentum_force', 'volatility_force', 'volume_force', 'sentiment_force',
                     'trend_force', 'support_resistance_force', 'options_flow_force', 'market_breadth_force']

            for force in forces:
                force_values = [row[force] for row in data if row[force] is not None]
                signal_values = [row['signal_strength'] for row in data if row[force] is not None and row['signal_strength'] is not None]

                if len(force_values) > 10:
                    # Simple correlation calculation
                    correlation = self._calculate_correlation(force_values, signal_values)
                    optimization_results['force_correlations'][force] = correlation

            # Generate threshold recommendations
            high_signal_data = [row for row in data if (row['signal_strength'] or 0) > 0.7]
            if high_signal_data:
                optimization_results['threshold_recommendations'] = {
                    'high_signal_conditions': f"Found {len(high_signal_data)} high-signal periods",
                    'avg_forces_during_high_signal': {
                        force: sum(row[force] or 0 for row in high_signal_data) / len(high_signal_data)
                        for force in forces
                    }
                }

            return optimization_results

        except Exception as e:
            self.logger.error(f"Failed to optimize radar thresholds: {e}")
            return {'error': str(e)}

    def _calculate_correlation(self, x_values: list, y_values: list) -> float:
        """Calculate simple correlation coefficient."""
        try:
            if len(x_values) != len(y_values) or len(x_values) < 2:
                return 0.0

            n = len(x_values)
            sum_x = sum(x_values)
            sum_y = sum(y_values)
            sum_xy = sum(x * y for x, y in zip(x_values, y_values))
            sum_x2 = sum(x * x for x in x_values)
            sum_y2 = sum(y * y for y in y_values)

            numerator = n * sum_xy - sum_x * sum_y
            denominator = ((n * sum_x2 - sum_x * sum_x) * (n * sum_y2 - sum_y * sum_y)) ** 0.5

            if denominator == 0:
                return 0.0

            return numerator / denominator

        except:
            return 0.0

    # ===== UNIFIED AI INTEGRATION METHODS =====

    def _store_unified_learning_cycle_result(self, result: UnifiedLearningResult, cycle_type: str):
        """Store unified learning cycle result with schema validation."""
        try:
            # Validate schema compliance
            if not result.eots_schema_compliance:
                self.logger.warning(f"⚠️ Learning result failed schema validation - storing with warning")

            cursor = self.db_manager._conn.cursor()
            cursor.execute("""
                INSERT INTO learning_cycle_results
                (timestamp, symbol, cycle_type, lookback_days, insights_count,
                 parameters_updated, confidence_score, learning_summary, result_data)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                result.timestamp,
                result.symbol,
                cycle_type,
                30,  # Default lookback
                len(result.new_insights),
                len(result.parameter_optimizations),
                result.confidence_evolution,
                f"Unified AI Learning: {len(result.new_insights)} insights, {len(result.pattern_adaptations)} adaptations",
                json.dumps({
                    'insights': result.new_insights,
                    'adaptations': result.pattern_adaptations,
                    'optimizations': result.parameter_optimizations,
                    'performance_validation': result.performance_validation,
                    'schema_compliance': result.eots_schema_compliance
                })
            ))

            self.logger.info(f"✅ Stored unified learning cycle result for {result.symbol}")

        except Exception as e:
            self.logger.error(f"Failed to store unified learning cycle result: {e}")

    def _validate_unified_learning_results(self, result: UnifiedLearningResult) -> Dict[str, Any]:
        """Validate unified learning results with schema compliance."""
        try:
            validation_result = {
                'validation_passed': True,
                'reason': '',
                'schema_compliance': result.eots_schema_compliance,
                'confidence_score': result.confidence_evolution,
                'insights_quality': len(result.new_insights) > 0,
                'optimization_quality': len(result.parameter_optimizations) > 0
            }

            # Check schema compliance
            if not result.eots_schema_compliance:
                validation_result['validation_passed'] = False
                validation_result['reason'] = 'Failed EOTS schema validation'
                return validation_result

            # Check confidence threshold
            if result.confidence_evolution < 0.6:
                validation_result['validation_passed'] = False
                validation_result['reason'] = f'Low confidence score: {result.confidence_evolution:.2f}'
                return validation_result

            # Check insights quality
            if len(result.new_insights) == 0:
                validation_result['validation_passed'] = False
                validation_result['reason'] = 'No new insights generated'
                return validation_result

            # Check for performance validation
            if not result.performance_validation or result.performance_validation.get('status') == 'failed':
                validation_result['validation_passed'] = False
                validation_result['reason'] = 'Performance validation failed'
                return validation_result

            validation_result['reason'] = 'All validation checks passed'
            return validation_result

        except Exception as e:
            self.logger.error(f"Failed to validate unified learning results: {e}")
            return {
                'validation_passed': False,
                'reason': f'Validation error: {str(e)}',
                'schema_compliance': False
            }

    async def generate_unified_intelligence_for_bundle(self,
                                                     bundle: FinalAnalysisBundleV2_5,
                                                     symbol: str):
        """Generate unified intelligence analysis for a validated bundle."""
        try:
            self.logger.info(f"🧠 Generating unified intelligence for {symbol}")

            # Validate bundle against EOTS schemas
            if not isinstance(bundle, FinalAnalysisBundleV2_5):
                raise ValueError("Bundle must be validated FinalAnalysisBundleV2_5")

            # Generate unified intelligence
            intelligence_analysis = await self.unified_ai_system.generate_unified_intelligence(bundle, symbol)

            # Validate schema compliance
            if not intelligence_analysis.validation_against_schemas:
                self.logger.warning(f"⚠️ Intelligence analysis failed schema validation for {symbol}")

            self.logger.info(f"✅ Unified intelligence generated with conviction: {intelligence_analysis.ai_conviction_score:.2f}")

            return intelligence_analysis

        except Exception as e:
            self.logger.error(f"❌ Failed to generate unified intelligence: {str(e)}")
            raise

    def get_unified_learning_status(self) -> Dict[str, Any]:
        """Get current status of unified learning system."""
        try:
            return {
                'system_type': 'Unified AI Intelligence System',
                'pydantic_ai_enabled': self.unified_ai_system.pydantic_ai_enabled,
                'schema_compliance': True,
                'learning_cache_size': len(self.unified_ai_system.learning_cache),
                'analysis_cache_size': len(self.unified_ai_system.analysis_cache),
                'pattern_cache_size': len(self.unified_ai_system.pattern_cache),
                'optimization_history_size': len(self.unified_ai_system.optimization_history),
                'database_path': str(self.unified_ai_system.intelligence_db_path),
                'last_updated': datetime.now().isoformat()
            }
        except Exception as e:
            self.logger.error(f"Failed to get unified learning status: {e}")
            return {'error': str(e)}

# ===== CONVENIENCE FUNCTIONS FOR SYSTEM INTEGRATION =====

def get_adaptive_learning_integration(config_manager, database_manager) -> AdaptiveLearningIntegrationV2_5:
    """Get or create adaptive learning integration instance."""
    return AdaptiveLearningIntegrationV2_5(database_manager, config_manager)

async def run_daily_unified_learning(symbol: str, config_manager, database_manager) -> UnifiedLearningResult:
    """Convenience function to run daily unified learning."""
    integration = get_adaptive_learning_integration(config_manager, database_manager)
    return await integration.run_daily_learning_cycle(symbol)

async def run_weekly_unified_learning(symbol: str, config_manager, database_manager) -> UnifiedLearningResult:
    """Convenience function to run weekly unified learning."""
    integration = get_adaptive_learning_integration(config_manager, database_manager)
    return await integration.run_weekly_deep_learning(symbol)
